/**
 * Unsplash Service for DocForge AI
 * Handles image fetching and integration for document content
 */

import { createApi } from 'unsplash-js';

// Initialize Unsplash API
const UNSPLASH_ACCESS_KEY = import.meta.env.VITE_UNSPLASH_ACCESS_KEY;

let unsplash = null;

if (UNSPLASH_ACCESS_KEY) {
  unsplash = createApi({
    accessKey: UNSPLASH_ACCESS_KEY,
  });
}

/**
 * Search for images based on query terms
 * @param {string} query - Search query for images
 * @param {number} count - Number of images to fetch (default: 3)
 * @param {string} orientation - Image orientation preference
 * @returns {Promise<Array>} Array of image objects
 */
export const searchImages = async (query, count = 3, orientation = 'landscape') => {
  try {
    if (!unsplash || !UNSPLASH_ACCESS_KEY || UNSPLASH_ACCESS_KEY === 'your-unsplash-access-key-here') {
      if (import.meta.env.DEV) {
        console.warn('Unsplash not configured (missing or placeholder API key), returning mock images for query:', query);
      }
      return getMockImages(query, count);
    }

    const result = await unsplash.search.getPhotos({
      query: query,
      page: 1,
      perPage: count,
      orientation: orientation,
      orderBy: 'relevant',
    });

    if (result.errors) {
      if (import.meta.env.DEV) {
        console.error('Unsplash API errors:', result.errors);
      }
      return getMockImages(query, count);
    }

    const images = result.response.results.map(photo => ({
      id: photo.id,
      url: photo.urls.regular,
      thumbnailUrl: photo.urls.small,
      description: photo.description || photo.alt_description || query,
      photographer: photo.user.name,
      photographerUrl: photo.user.links.html,
      downloadUrl: photo.links.download_location,
      width: photo.width,
      height: photo.height,
      color: photo.color,
    }));

    return images;

  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error fetching images from Unsplash:', error);
    }
    return getMockImages(query, count);
  }
};

/**
 * Generate simple chapter-based image suggestions (no complex placement logic)
 * @param {Object} chapter - Chapter object with title and content
 * @param {Object} documentData - Document data for context
 * @returns {Object} Simple chapter suggestion object
 */
const generateChapterImageSuggestion = (chapter, documentData) => {
  return {
    chapterNumber: chapter.number,
    chapterTitle: chapter.title,
    searchQuery: generateSearchQuery(
      chapter.title,
      documentData.topicAndNiche?.mainTopic,
      documentData.topicAndNiche?.subNiches
    ),
    // Simple metadata for the chapter
    description: `Image suggestions for ${chapter.title}`,
    contextualHint: `Visual content to enhance Chapter ${chapter.number}: ${chapter.title}`
  };
};

/**
 * Generate image suggestions for document chapters
 * @param {Object} documentData - Complete document data
 * @param {Object} outline - Document outline with chapters
 * @returns {Promise<Object>} Object with chapter IDs as keys and image arrays as values
 */
export const generateImageSuggestions = async (documentData, outline) => {
  try {
    const suggestions = {};

    if (!outline?.chapters) {
      console.warn('No chapters found in outline, returning empty suggestions');
      return suggestions;
    }

    // Generate simple chapter-based image suggestions
    for (const chapter of outline.chapters) {
      const chapterSuggestion = generateChapterImageSuggestion(chapter, documentData);
      const images = await searchImages(chapterSuggestion.searchQuery, 3);

      suggestions[`chapter-${chapter.number}`] = {
        chapterTitle: chapter.title,
        chapterNumber: chapter.number,
        searchQuery: chapterSuggestion.searchQuery,
        images: images,
        description: chapterSuggestion.description,
        contextualHint: chapterSuggestion.contextualHint,
        // Simplified: no complex placement logic, just chapter-boundary placement
        placementType: 'chapter-boundary',
        // Legacy support - keep existing structure for backward compatibility
        suggestedPlacements: [
          {
            type: 'chapter-header',
            position: 'before-chapter',
            description: `Image suggestions for ${chapter.title}`
          }
        ]
      };
    }

    return suggestions;

  } catch (error) {
    console.error('Error generating image suggestions:', error);
    return {};
  }
};

/**
 * Enhanced AI-powered search query generation for contextual image matching
 * Analyzes chapter content and document context to create optimal search terms
 * @param {string} chapterTitle - Chapter title
 * @param {string} mainTopic - Main document topic
 * @param {Array} subNiches - Selected sub-niches
 * @returns {string} Contextually optimized search query
 */
const generateSearchQuery = (chapterTitle, mainTopic, subNiches = []) => {
  // Enhanced keyword extraction with semantic understanding
  const extractMeaningfulKeywords = (text, maxWords = 3) => {
    // Common stop words to filter out
    const stopWords = new Set([
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above',
      'below', 'between', 'among', 'chapter', 'introduction', 'conclusion', 'overview',
      'guide', 'how', 'what', 'why', 'when', 'where', 'which', 'that', 'this', 'these',
      'those', 'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
      'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
      'might', 'must', 'can', 'step', 'steps', 'part', 'section'
    ]);

    return text
      .toLowerCase()
      .replace(/chapter \d+:?\s*/i, '') // Remove "Chapter X:" prefix
      .replace(/[^\w\s]/g, ' ') // Replace special characters with spaces
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, maxWords);
  };

  // Extract contextual keywords from chapter title
  const chapterKeywords = extractMeaningfulKeywords(chapterTitle, 3);

  // Extract topic keywords with priority weighting
  const topicKeywords = extractMeaningfulKeywords(mainTopic, 2);

  // Smart sub-niche selection based on relevance
  let subNicheKeyword = '';
  if (subNiches.length > 0) {
    // Find the most relevant sub-niche based on chapter content
    const chapterText = chapterTitle.toLowerCase();
    const relevantSubNiche = subNiches.find(niche =>
      chapterText.includes(niche.name?.toLowerCase().split(' ')[0])
    ) || subNiches[0];

    subNicheKeyword = relevantSubNiche.name?.toLowerCase().split(' ')[0] || '';
  }

  // Intelligent keyword combination with context awareness
  const allKeywords = [];

  // Prioritize chapter-specific keywords (most relevant)
  allKeywords.push(...chapterKeywords);

  // Add topic keywords for context
  topicKeywords.forEach(keyword => {
    if (!allKeywords.includes(keyword)) {
      allKeywords.push(keyword);
    }
  });

  // Add sub-niche if it adds value and isn't redundant
  if (subNicheKeyword && !allKeywords.includes(subNicheKeyword)) {
    allKeywords.push(subNicheKeyword);
  }

  // Enhanced query optimization for better image matching
  const optimizedQuery = allKeywords.slice(0, 4).join(' ');

  // Add visual context hints for better image selection
  const visualContextHints = {
    'strategy': 'business planning',
    'analysis': 'data charts',
    'development': 'coding workspace',
    'design': 'creative workspace',
    'marketing': 'business meeting',
    'management': 'professional office',
    'learning': 'education books',
    'research': 'academic study',
    'innovation': 'technology concept',
    'growth': 'business success'
  };

  // Check if any keywords match visual context hints
  for (const [keyword, hint] of Object.entries(visualContextHints)) {
    if (optimizedQuery.includes(keyword)) {
      return `${optimizedQuery} ${hint}`;
    }
  }

  return optimizedQuery;
};

/**
 * Enhanced Mock AI Image System for DocForge AI
 * Provides contextually relevant, high-quality Unsplash images based on content analysis
 * @param {string} query - Search query derived from chapter content
 * @param {number} count - Number of images to return (default: 3)
 * @returns {Array} Contextually matched mock image objects with Unsplash structure
 */
const getMockImages = (query, count) => {
  // Comprehensive categorized image collection for different content types
  const imageCategories = {
    business: [
      {
        id: 'business-workspace-1',
        url: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=300&h=200&fit=crop&auto=format',
        description: 'Professional business workspace with laptop and documents',
        photographer: 'Beatriz Pérez Moya',
        photographerUrl: 'https://unsplash.com/@beatriz_perez',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#4A90E2',
        keywords: ['business', 'workspace', 'professional', 'office', 'laptop', 'work']
      },
      {
        id: 'business-meeting-1',
        url: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=300&h=200&fit=crop&auto=format',
        description: 'Professional business meeting and team collaboration',
        photographer: 'Campaign Creators',
        photographerUrl: 'https://unsplash.com/@campaign_creators',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#7B68EE',
        keywords: ['meeting', 'collaboration', 'team', 'business', 'discussion', 'strategy']
      },
      {
        id: 'business-strategy-1',
        url: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop&auto=format',
        description: 'Strategic planning and business analysis with charts',
        photographer: 'Carlos Muza',
        photographerUrl: 'https://unsplash.com/@kmuza',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#2ECC71',
        keywords: ['strategy', 'planning', 'analysis', 'charts', 'data', 'growth']
      }
    ],
    technology: [
      {
        id: 'tech-innovation-1',
        url: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=300&h=200&fit=crop&auto=format',
        description: 'Modern technology and digital innovation concept',
        photographer: 'Luca Bravo',
        photographerUrl: 'https://unsplash.com/@lucabravo',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#45B7D1',
        keywords: ['technology', 'innovation', 'digital', 'modern', 'computer', 'coding']
      },
      {
        id: 'tech-coding-1',
        url: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=300&h=200&fit=crop&auto=format',
        description: 'Programming and software development workspace',
        photographer: 'Christopher Gower',
        photographerUrl: 'https://unsplash.com/@cgower',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#1A1A1A',
        keywords: ['coding', 'programming', 'development', 'software', 'computer', 'screen']
      },
      {
        id: 'tech-ai-1',
        url: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=300&h=200&fit=crop&auto=format',
        description: 'Artificial intelligence and machine learning concept',
        photographer: 'Alex Knight',
        photographerUrl: 'https://unsplash.com/@agkdesign',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#FF6B35',
        keywords: ['ai', 'artificial', 'intelligence', 'machine', 'learning', 'robot']
      }
    ],
    education: [
      {
        id: 'education-books-1',
        url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop&auto=format',
        description: 'Books and learning materials for education and study',
        photographer: 'Ben White',
        photographerUrl: 'https://unsplash.com/@benwhitephotography',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#4ECDC4',
        keywords: ['education', 'books', 'learning', 'study', 'knowledge', 'reading']
      },
      {
        id: 'education-classroom-1',
        url: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=300&h=200&fit=crop&auto=format',
        description: 'Modern classroom and educational environment',
        photographer: 'Changbok Ko',
        photographerUrl: 'https://unsplash.com/@kochangbok',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#F39C12',
        keywords: ['classroom', 'education', 'teaching', 'learning', 'school', 'students']
      },
      {
        id: 'education-online-1',
        url: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=300&h=200&fit=crop&auto=format',
        description: 'Online learning and digital education concept',
        photographer: 'Brooke Cagle',
        photographerUrl: 'https://unsplash.com/@brookecagle',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#9B59B6',
        keywords: ['online', 'learning', 'digital', 'education', 'computer', 'study']
      }
    ],
    creative: [
      {
        id: 'creative-design-1',
        url: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=300&h=200&fit=crop&auto=format',
        description: 'Creative workspace with design tools and materials',
        photographer: 'Hal Gatewood',
        photographerUrl: 'https://unsplash.com/@halacious',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#FF6B6B',
        keywords: ['creative', 'design', 'art', 'workspace', 'tools', 'inspiration']
      },
      {
        id: 'creative-art-1',
        url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=300&h=200&fit=crop&auto=format',
        description: 'Artistic creation and creative expression',
        photographer: 'Russn_fckr',
        photographerUrl: 'https://unsplash.com/@russn_fckr',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#E74C3C',
        keywords: ['art', 'creative', 'painting', 'expression', 'artistic', 'inspiration']
      },
      {
        id: 'creative-photography-1',
        url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=300&h=200&fit=crop&auto=format',
        description: 'Photography and visual storytelling concept',
        photographer: 'Nordwood Themes',
        photographerUrl: 'https://unsplash.com/@nordwood',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#34495E',
        keywords: ['photography', 'camera', 'visual', 'storytelling', 'creative', 'capture']
      }
    ],
    health: [
      {
        id: 'health-wellness-1',
        url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop&auto=format',
        description: 'Health and wellness lifestyle concept',
        photographer: 'Brooke Lark',
        photographerUrl: 'https://unsplash.com/@brookelark',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#27AE60',
        keywords: ['health', 'wellness', 'lifestyle', 'fitness', 'nutrition', 'wellbeing']
      },
      {
        id: 'health-medical-1',
        url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop&auto=format',
        thumbnailUrl: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=200&fit=crop&auto=format',
        description: 'Medical and healthcare professional environment',
        photographer: 'National Cancer Institute',
        photographerUrl: 'https://unsplash.com/@nci',
        downloadUrl: '#',
        width: 800,
        height: 600,
        color: '#3498DB',
        keywords: ['medical', 'healthcare', 'doctor', 'hospital', 'treatment', 'care']
      }
    ]
  };

  // Smart category detection based on query content
  const detectCategory = (searchQuery) => {
    const query = searchQuery.toLowerCase();

    if (query.includes('business') || query.includes('management') || query.includes('strategy') ||
        query.includes('marketing') || query.includes('finance') || query.includes('corporate')) {
      return 'business';
    }
    if (query.includes('technology') || query.includes('tech') || query.includes('digital') ||
        query.includes('software') || query.includes('coding') || query.includes('ai') ||
        query.includes('computer') || query.includes('programming')) {
      return 'technology';
    }
    if (query.includes('education') || query.includes('learning') || query.includes('teaching') ||
        query.includes('study') || query.includes('academic') || query.includes('school') ||
        query.includes('knowledge')) {
      return 'education';
    }
    if (query.includes('creative') || query.includes('design') || query.includes('art') ||
        query.includes('photography') || query.includes('visual') || query.includes('inspiration')) {
      return 'creative';
    }
    if (query.includes('health') || query.includes('wellness') || query.includes('medical') ||
        query.includes('fitness') || query.includes('nutrition') || query.includes('care')) {
      return 'health';
    }

    // Default to business for general topics
    return 'business';
  };

  const category = detectCategory(query);
  const categoryImages = imageCategories[category] || imageCategories.business;
  const mockImages = [];

  for (let i = 0; i < count; i++) {
    // Select images from the detected category, cycling through if needed
    const baseImage = categoryImages[i % categoryImages.length];

    mockImages.push({
      ...baseImage,
      id: `mock-${category}-${query.replace(/\s+/g, '-')}-${i + 1}`,
      description: `${query} - ${baseImage.description}`,
    });
  }

  return mockImages;
};

/**
 * Download and track image usage (required by Unsplash API)
 * @param {string} downloadUrl - Download URL from Unsplash
 */
export const trackImageDownload = async (downloadUrl) => {
  try {
    if (!unsplash || !downloadUrl || downloadUrl === '#') {
      return;
    }

    // This is required by Unsplash API terms to track downloads
    await fetch(downloadUrl);
  } catch (error) {
    console.error('Error tracking image download:', error);
  }
};

/**
 * Get image placement suggestions for content
 * @param {string} content - Text content
 * @param {Array} images - Available images
 * @returns {Array} Placement suggestions
 */
export const getImagePlacementSuggestions = (content, images) => {
  const suggestions = [];
  const paragraphs = content.split('\n\n');
  
  if (paragraphs.length < 2 || images.length === 0) {
    return suggestions;
  }

  // Suggest image after introduction (first paragraph)
  if (paragraphs.length > 1) {
    suggestions.push({
      position: 1, // After first paragraph
      image: images[0],
      type: 'content-break',
      description: 'Visual break after introduction'
    });
  }

  // Suggest image in middle of content
  if (paragraphs.length > 4 && images.length > 1) {
    const middlePosition = Math.floor(paragraphs.length / 2);
    suggestions.push({
      position: middlePosition,
      image: images[1],
      type: 'section-illustration',
      description: 'Section illustration'
    });
  }

  // Suggest image near end if content is long
  if (paragraphs.length > 6 && images.length > 2) {
    suggestions.push({
      position: paragraphs.length - 2,
      image: images[2],
      type: 'conclusion-visual',
      description: 'Visual support for conclusion'
    });
  }

  return suggestions;
};

/**
 * Check if Unsplash is configured
 * @returns {boolean} Whether Unsplash API is available
 */
export const isUnsplashConfigured = () => {
  return !!(UNSPLASH_ACCESS_KEY && unsplash);
};

/**
 * Get Unsplash configuration status
 * @returns {Object} Configuration status and details
 */
export const getUnsplashStatus = () => {
  return {
    configured: isUnsplashConfigured(),
    hasAccessKey: !!UNSPLASH_ACCESS_KEY,
    apiInitialized: !!unsplash,
  };
};
