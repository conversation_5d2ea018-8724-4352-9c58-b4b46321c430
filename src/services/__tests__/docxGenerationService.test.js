/**
 * Test suite for DOCX Generation Service
 * Tests core document structure creation and title page generation
 */

import {
    generateDocxDocument,
    createTitlePage,
    createDocumentStructure,
    createChapterContent
} from '../docxGenerationService';

// Mock the docx library
jest.mock('docx', () => ({
    Document: jest.fn().mockImplementation((config) => ({ config })),
    Packer: {
        toBlob: jest.fn().mockResolvedValue(new Blob(['mock-docx-content'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }))
    },
    Paragraph: jest.fn().mockImplementation((config) => ({ type: 'paragraph', ...config })),
    TextRun: jest.fn().mockImplementation((config) => ({ type: 'textrun', ...config })),
    HeadingLevel: {
        HEADING_1: 'HEADING_1'
    },
    AlignmentType: {
        CENTER: 'CENTER'
    }
}));

describe('DocxGenerationService', () => {
    const mockDocumentData = {
        title: 'Test Document',
        author: 'Test Author',
        description: 'Test Description'
    };

    const mockProcessedContent = {
        chapters: [
            {
                number: 1,
                title: 'First Chapter',
                content: 'This is the content of the first chapter.'
            },
            {
                number: 2,
                title: 'Second Chapter',
                content: 'This is the content of the second chapter.'
            }
        ]
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('generateDocxDocument', () => {
        test('should generate a basic DOCX document with title page', async () => {
            const result = await generateDocxDocument(mockDocumentData);

            expect(result.success).toBe(true);
            expect(result.blob).toBeInstanceOf(Blob);
            expect(result.error).toBeUndefined();
        });

        test('should handle document generation with processed content', async () => {
            const result = await generateDocxDocument(mockDocumentData, mockProcessedContent);

            expect(result.success).toBe(true);
            expect(result.blob).toBeInstanceOf(Blob);
        });

        test('should handle missing document data gracefully', async () => {
            const result = await generateDocxDocument({});

            expect(result.success).toBe(true);
            expect(result.blob).toBeInstanceOf(Blob);
        });

        test('should handle errors during document generation', async () => {
            // Mock Packer.toBlob to throw an error
            const { Packer } = require('docx');
            Packer.toBlob.mockRejectedValueOnce(new Error('Generation failed'));

            const result = await generateDocxDocument(mockDocumentData);

            expect(result.success).toBe(false);
            expect(result.error).toBe('Generation failed');
            expect(result.blob).toBeUndefined();

            // Restore the mock
            Packer.toBlob.mockResolvedValue(new Blob(['mock-docx-content'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }));
        });
    });

    describe('createTitlePage', () => {
        test('should create title page with all elements', () => {
            const titlePage = createTitlePage('Test Title', 'Test Author', 'Test Description');

            expect(titlePage).toHaveLength(4); // Spacing + title + author + description
            expect(titlePage[1].children[0].text).toBe('Test Title');
            expect(titlePage[1].children[0].bold).toBe(true);
            expect(titlePage[1].children[0].size).toBe(48);
            expect(titlePage[2].children[0].text).toBe('by Test Author');
            expect(titlePage[3].children[0].text).toBe('Test Description');
            expect(titlePage[3].children[0].italics).toBe(true);
        });

        test('should create title page with only title', () => {
            const titlePage = createTitlePage('Test Title', null, null);

            expect(titlePage).toHaveLength(2); // Spacing + title only
            expect(titlePage[1].children[0].text).toBe('Test Title');
        });

        test('should create title page with title and author only', () => {
            const titlePage = createTitlePage('Test Title', 'Test Author', null);

            expect(titlePage).toHaveLength(3); // Spacing + title + author
            expect(titlePage[1].children[0].text).toBe('Test Title');
            expect(titlePage[2].children[0].text).toBe('by Test Author');
        });

        test('should handle empty parameters', () => {
            const titlePage = createTitlePage(null, null, null);

            expect(titlePage).toHaveLength(1); // Only spacing
        });
    });

    describe('createDocumentStructure', () => {
        test('should create document structure with title page and chapters', () => {
            const structure = createDocumentStructure(mockDocumentData, mockProcessedContent);

            expect(structure.title).toBe('Test Document');
            expect(structure.creator).toBe('Test Author');
            expect(structure.description).toBe('Test Description');
            expect(structure.sections).toHaveLength(3); // Title page + 2 chapters
        });

        test('should create document structure with only title page when no chapters', () => {
            const structure = createDocumentStructure(mockDocumentData, { chapters: [] });

            expect(structure.sections).toHaveLength(1); // Only title page
        });

        test('should handle missing processed content', () => {
            const structure = createDocumentStructure(mockDocumentData, null);

            expect(structure.sections).toHaveLength(1); // Only title page
        });

        test('should use default values for missing document data', () => {
            const structure = createDocumentStructure({}, null);

            expect(structure.title).toBe('Untitled Document');
            expect(structure.creator).toBe('DocForge AI');
            expect(structure.description).toBe('');
        });
    });

    describe('createChapterContent', () => {
        test('should create chapter content with title and content', () => {
            const chapter = {
                number: 1,
                title: 'Test Chapter',
                content: 'This is test content.'
            };

            const chapterContent = createChapterContent(chapter, 0);

            expect(chapterContent).toHaveLength(2); // Title + content
            expect(chapterContent[0].children[0].text).toBe('Chapter 1: Test Chapter');
            expect(chapterContent[0].children[0].bold).toBe(true);
            expect(chapterContent[1].children[0].text).toBe('This is test content.');
        });

        test('should handle chapter without explicit number', () => {
            const chapter = {
                title: 'Test Chapter',
                content: 'This is test content.'
            };

            const chapterContent = createChapterContent(chapter, 2);

            expect(chapterContent[0].children[0].text).toBe('Chapter 3: Test Chapter');
        });

        test('should handle chapter without title', () => {
            const chapter = {
                number: 1,
                content: 'This is test content.'
            };

            const chapterContent = createChapterContent(chapter, 0);

            expect(chapterContent[0].children[0].text).toBe('Chapter 1: Chapter 1');
        });

        test('should handle chapter without content', () => {
            const chapter = {
                number: 1,
                title: 'Test Chapter'
            };

            const chapterContent = createChapterContent(chapter, 0);

            expect(chapterContent).toHaveLength(1); // Only title, no content paragraph
        });
    });
});