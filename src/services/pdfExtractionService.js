import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

/**
 * PDF Content Extraction Service
 * Provides functionality to extract text content from PDF files
 * Follows the same pattern as docxExtractionService for consistency
 */

// File validation constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_EXTENSIONS = ['.pdf'];
const ALLOWED_MIME_TYPES = ['application/pdf'];

// Error message constants
const PDF_ERROR_MESSAGES = {
  PASSWORD_PROTECTED: "This PDF is password-protected. Please remove the password and try again.",
  SCANNED_PDF: "This appears to be a scanned PDF with limited text content. For best results, please use a text-based PDF.",
  CORRUPTED: "The PDF file appears to be corrupted or invalid. Please try a different file.",
  TOO_LARGE: "PDF file is too large (max 10MB). Please use a smaller file.",
  UNSUPPORTED_VERSION: "This PDF version is not supported. Please try saving it in a newer format.",
  NO_TEXT_CONTENT: "No readable text content found in this PDF. It may be image-based or corrupted.",
  EXTRACTION_FAILED: "Failed to extract content from the PDF. Please try a different file."
};

/**
 * Validate PDF file before processing
 * @param {File} file - The PDF file to validate
 * @returns {Object} Validation result with isValid flag and errors array
 */
export const validatePdfFile = (file) => {
  const errors = [];
  
  if (!file) {
    errors.push('No file provided');
    return { isValid: false, errors };
  }

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    errors.push(PDF_ERROR_MESSAGES.TOO_LARGE);
  }

  // Check file extension
  const fileName = file.name.toLowerCase();
  const hasValidExtension = ALLOWED_EXTENSIONS.some(ext => fileName.endsWith(ext));
  if (!hasValidExtension) {
    errors.push('File must be a PDF (.pdf)');
  }

  // Check MIME type
  if (!ALLOWED_MIME_TYPES.includes(file.type)) {
    errors.push('Invalid file type. Please select a PDF file.');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Extract text content from a single PDF page
 * @param {Object} page - PDF.js page object
 * @returns {Promise<Object>} Page text content and metadata
 */
const extractPageContent = async (page) => {
  try {
    const textContent = await page.getTextContent();
    const viewport = page.getViewport({ scale: 1.0 });
    
    let pageText = '';
    const textItems = [];
    
    // Process text items and build page content
    textContent.items.forEach((item) => {
      if (item.str.trim()) {
        textItems.push({
          text: item.str,
          x: item.transform[4],
          y: item.transform[5],
          width: item.width,
          height: item.height,
          fontSize: item.height
        });
        pageText += item.str + ' ';
      }
    });
    
    return {
      text: pageText.trim(),
      textItems,
      width: viewport.width,
      height: viewport.height
    };
  } catch (error) {
    console.error('Error extracting page content:', error);
    return {
      text: '',
      textItems: [],
      width: 0,
      height: 0
    };
  }
};

/**
 * Detect if PDF is primarily image-based (scanned)
 * @param {Array} pages - Array of page content objects
 * @returns {boolean} True if PDF appears to be scanned/image-based
 */
const isScannedPdf = (pages) => {
  const totalPages = pages.length;
  let pagesWithMinimalText = 0;
  
  pages.forEach(page => {
    // If page has very few text items or very short text, it's likely scanned
    if (page.textItems.length < 10 || page.text.length < 100) {
      pagesWithMinimalText++;
    }
  });
  
  // If more than 50% of pages have minimal text, consider it scanned
  return (pagesWithMinimalText / totalPages) > 0.5;
};

/**
 * Extract title from PDF content
 * @param {Array} pages - Array of page content objects
 * @param {Object} metadata - PDF metadata
 * @returns {string} Extracted title
 */
const extractTitle = (pages, metadata) => {
  // First try metadata title
  if (metadata.info && metadata.info.Title && metadata.info.Title.trim()) {
    return metadata.info.Title.trim();
  }
  
  // Then try to find title from first page content
  if (pages.length > 0 && pages[0].textItems.length > 0) {
    // Look for the largest text item on the first page (likely title)
    const sortedBySize = pages[0].textItems
      .filter(item => item.text.trim().length > 3)
      .sort((a, b) => b.fontSize - a.fontSize);
    
    if (sortedBySize.length > 0) {
      return sortedBySize[0].text.trim();
    }
  }
  
  return 'Untitled Document';
};

/**
 * Detect headings in PDF content based on font size and positioning
 * @param {Array} pages - Array of page content objects
 * @returns {Array} Array of detected headings
 */
const detectHeadings = (pages) => {
  const headings = [];

  pages.forEach((page, pageIndex) => {
    if (page.textItems.length === 0) return;

    // Calculate average font size for the page
    const avgFontSize = page.textItems.reduce((sum, item) => sum + item.fontSize, 0) / page.textItems.length;

    // Find text items that are significantly larger than average (potential headings)
    page.textItems.forEach((item) => {
      if (item.fontSize > avgFontSize * 1.2 && item.text.trim().length > 3) {
        headings.push({
          text: item.text.trim(),
          level: Math.min(Math.floor((item.fontSize - avgFontSize) / 2) + 1, 6),
          page: pageIndex + 1,
          fontSize: item.fontSize
        });
      }
    });
  });

  return headings;
};

/**
 * Extract paragraphs from PDF content
 * @param {Array} pages - Array of page content objects
 * @returns {Array} Array of paragraphs
 */
const extractParagraphs = (pages) => {
  const paragraphs = [];

  pages.forEach((page, pageIndex) => {
    if (page.text.trim()) {
      // Split page text into potential paragraphs
      const pageParagraphs = page.text
        .split(/\n\s*\n|\.\s+(?=[A-Z])/)
        .filter(p => p.trim().length > 20)
        .map(p => ({
          text: p.trim(),
          page: pageIndex + 1,
          wordCount: p.trim().split(/\s+/).length
        }));

      paragraphs.push(...pageParagraphs);
    }
  });

  return paragraphs;
};

/**
 * Build document structure from extracted content
 * @param {Array} pages - Array of page content objects
 * @param {Array} headings - Array of detected headings
 * @returns {Object} Document structure
 */
const buildDocumentStructure = (pages, headings) => {
  return {
    totalPages: pages.length,
    headingCount: headings.length,
    hasStructure: headings.length > 0,
    sections: headings.map(heading => ({
      title: heading.text,
      level: heading.level,
      page: heading.page
    }))
  };
};

/**
 * Count words in extracted text
 * @param {string} text - Text content
 * @returns {number} Word count
 */
const countWords = (text) => {
  if (!text || typeof text !== 'string') return 0;
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
};

/**
 * Infer document type based on content analysis
 * @param {Object} extractedData - Extracted content data
 * @returns {string} Inferred document type
 */
const inferDocumentType = (extractedData) => {
  const { content, headings, wordCount } = extractedData;

  if (!content) return 'other';

  const contentLower = content.toLowerCase();

  // Academic paper indicators
  if (contentLower.includes('abstract') &&
      contentLower.includes('references') &&
      headings.length > 3) {
    return 'academic'; // Fixed: return 'academic' instead of 'academic-paper'
  }

  // Business report indicators
  if (contentLower.includes('executive summary') ||
      contentLower.includes('conclusion') ||
      contentLower.includes('recommendations')) {
    return 'business'; // Fixed: return 'business' instead of 'business-report'
  }

  // Technical/business document indicators
  if (contentLower.includes('whitepaper') ||
      contentLower.includes('technical report') ||
      contentLower.includes('position paper') ||
      contentLower.includes('report') ||
      contentLower.includes('survey') ||
      contentLower.includes('data analysis')) {
    return 'business';
  }

  // Instructional content indicators
  if (contentLower.includes('manual') ||
      contentLower.includes('instruction') ||
      contentLower.includes('user guide')) {
    return 'guide';
  }

  // Guide indicators
  if (contentLower.includes('step') &&
      contentLower.includes('how to') ||
      headings.length > 5) {
    return 'guide';
  }

  // eBook indicators (longer content with chapters)
  if (wordCount > 10000 && headings.length > 8) {
    return 'ebook';
  }

  return 'ebook'; // Default fallback to valid document type
};

/**
 * Parse and structure PDF content
 * @param {Array} pages - Array of page content objects
 * @param {Object} metadata - PDF metadata
 * @param {File} file - Original file object
 * @returns {Object} Structured content data
 */
const parsePdfContent = (pages, metadata, file) => {
  const allText = pages.map(page => page.text).join('\n\n');
  const headings = detectHeadings(pages);
  const paragraphs = extractParagraphs(pages);
  const title = extractTitle(pages, metadata);
  const structure = buildDocumentStructure(pages, headings);
  const wordCount = countWords(allText);

  return {
    content: allText,
    title,
    headings,
    paragraphs,
    structure,
    wordCount,
    metadata: {
      fileName: file.name,
      fileSize: file.size,
      totalPages: pages.length,
      author: metadata.info?.Author || '',
      subject: metadata.info?.Subject || '',
      creator: metadata.info?.Creator || '',
      producer: metadata.info?.Producer || '',
      creationDate: metadata.info?.CreationDate || null,
      modificationDate: metadata.info?.ModDate || null
    }
  };
};

/**
 * Test PDF.js library functionality
 * @returns {Object} Test result with success flag and message
 */
export const testPdfLibrary = () => {
  try {
    if (typeof pdfjsLib === 'undefined') {
      return {
        success: false,
        error: 'PDF.js library not loaded'
      };
    }

    if (!pdfjsLib.getDocument) {
      return {
        success: false,
        error: 'PDF.js getDocument function not available'
      };
    }

    return {
      success: true,
      message: `PDF.js library loaded successfully (version ${pdfjsLib.version})`
    };
  } catch (error) {
    return {
      success: false,
      error: `PDF.js library test failed: ${error.message}`
    };
  }
};

/**
 * Main function to extract content from PDF file
 * @param {File} file - PDF file to process
 * @returns {Promise<Object>} Extraction result with success flag and data
 */
export const extractContentFromPdf = async (file) => {
  console.log('Starting PDF extraction for:', file.name);

  // Validate file first
  const validation = validatePdfFile(file);
  if (!validation.isValid) {
    console.error('PDF validation failed:', validation.errors);
    return {
      success: false,
      data: {
        extractionError: validation.errors.join(', '),
        extractionStatus: 'validation_failed'
      }
    };
  }

  try {
    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();
    console.log('File converted to array buffer, size:', arrayBuffer.byteLength);

    // Load PDF document
    const loadingTask = pdfjsLib.getDocument(arrayBuffer);
    const pdf = await loadingTask.promise;
    console.log('PDF loaded successfully, pages:', pdf.numPages);

    // Extract metadata
    const metadata = await pdf.getMetadata();
    console.log('PDF metadata extracted:', metadata);

    // Extract content from all pages
    const pages = [];
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      console.log(`Extracting content from page ${pageNum}/${pdf.numPages}`);
      const page = await pdf.getPage(pageNum);
      const pageContent = await extractPageContent(page);
      pages.push(pageContent);
    }

    console.log('All pages processed, total pages:', pages.length);

    // Check if PDF is primarily scanned/image-based
    if (isScannedPdf(pages)) {
      console.warn('PDF appears to be scanned/image-based');
      return {
        success: false,
        data: {
          extractionError: PDF_ERROR_MESSAGES.SCANNED_PDF,
          extractionStatus: 'scanned_pdf'
        }
      };
    }

    // Check if any text content was extracted
    const totalTextLength = pages.reduce((sum, page) => sum + page.text.length, 0);
    if (totalTextLength < 50) {
      console.warn('Insufficient text content extracted');
      return {
        success: false,
        data: {
          extractionError: PDF_ERROR_MESSAGES.NO_TEXT_CONTENT,
          extractionStatus: 'no_text_content'
        }
      };
    }

    // Parse and structure the content
    const extractedData = parsePdfContent(pages, metadata, file);
    console.log('Content parsing completed:', extractedData);

    return {
      success: true,
      data: {
        fileName: file.name,
        fileSize: file.size,
        extractedContent: extractedData.content,
        originalTitle: extractedData.title,
        structure: extractedData.structure,
        wordCount: extractedData.wordCount,
        extractedAt: new Date().toISOString(),
        extractionStatus: 'success',
        extractionError: '',
        // Additional metadata
        headings: extractedData.headings,
        paragraphs: extractedData.paragraphs,
        documentType: inferDocumentType(extractedData),
        metadata: extractedData.metadata,
        // Note: No sourceUrl for file uploads - this distinguishes from URL imports
        sourceUrl: null
      }
    };

  } catch (error) {
    console.error('PDF extraction error:', error);

    // Handle specific PDF.js errors
    let errorMessage = PDF_ERROR_MESSAGES.EXTRACTION_FAILED;

    if (error.name === 'PasswordException') {
      errorMessage = PDF_ERROR_MESSAGES.PASSWORD_PROTECTED;
    } else if (error.name === 'InvalidPDFException') {
      errorMessage = PDF_ERROR_MESSAGES.CORRUPTED;
    } else if (error.name === 'MissingPDFException') {
      errorMessage = PDF_ERROR_MESSAGES.CORRUPTED;
    } else if (error.name === 'UnexpectedResponseException') {
      errorMessage = PDF_ERROR_MESSAGES.CORRUPTED;
    }

    return {
      success: false,
      data: {
        extractionError: errorMessage,
        extractionStatus: 'extraction_failed',
        technicalError: error.message
      }
    };
  }
};
