// Session Repair Utility
// This utility specifically targets and fixes issues with existing sessions getting stuck

import { supabase } from '../lib/supabase';

export const sessionRepair = {
  // Check if we need to repair the session
  async checkAndRepair() {
    console.log('🔧 Checking if session needs repair...');
    
    try {
      // First, check if we're in a stuck state
      const loadStartTime = sessionStorage.getItem('docforge_load_start_time');
      const now = Date.now();
      
      if (loadStartTime && (now - parseInt(loadStartTime) > 3000)) {
        console.warn('⚠️ Detected potential stuck session - attempting repair');
        
        // Get current session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          // We have a session but we're stuck - likely a token or cache issue
          return await this.repairExistingSession(session);
        } else {
          // We don't have a session but might have stale tokens
          return await this.clearStaleTokens();
        }
      }
      
      return { repaired: false, message: 'No repair needed' };
    } catch (err) {
      console.error('❌ Session check failed:', err);
      return { repaired: false, error: err.message };
    }
  },
  
  // Repair an existing session that's stuck
  async repairExistingSession(session) {
    console.log('🔧 Repairing existing session...');
    
    try {
      // 1. Check for token expiration
      const expiresAt = session.expires_at * 1000;
      const now = Date.now();
      const timeUntilExpiry = expiresAt - now;
      
      if (timeUntilExpiry < 5 * 60 * 1000) { // Less than 5 minutes
        console.log('⚠️ Session token expiring soon - refreshing');
        
        // Try to refresh the token
        const { data, error } = await supabase.auth.refreshSession();
        
        if (error) {
          console.error('❌ Token refresh failed:', error);
          return await this.forceReset();
        }
        
        console.log('✅ Token refreshed successfully');
      }
      
      // 2. Clear profile cache which might be corrupted
      this.clearProfileCache();
      
      // 3. Force reload user data
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        console.error('❌ User data fetch failed:', userError);
        return await this.forceReset();
      }
      
      // 4. Clear any loading markers
      sessionStorage.removeItem('docforge_load_start_time');
      
      // 5. Force reload the page to restart with clean state
      console.log('✅ Session repaired successfully - reloading page');
      window.location.reload();
      
      return { repaired: true, action: 'refreshed_session' };
    } catch (err) {
      console.error('❌ Session repair failed:', err);
      return await this.forceReset();
    }
  },
  
  // Clear stale tokens when no session is found
  async clearStaleTokens() {
    console.log('🔧 Clearing stale tokens...');
    
    try {
      // 1. Clear Supabase token
      localStorage.removeItem('sb-supabase-auth-token');
      
      // 2. Clear any profile cache
      this.clearProfileCache();
      
      // 3. Clear any loading markers
      sessionStorage.removeItem('docforge_load_start_time');
      
      // 4. Sign out to be safe
      await supabase.auth.signOut();
      
      // 5. Force reload the page
      console.log('✅ Stale tokens cleared - reloading page');
      window.location.reload();
      
      return { repaired: true, action: 'cleared_stale_tokens' };
    } catch (err) {
      console.error('❌ Token clearing failed:', err);
      return await this.forceReset();
    }
  },
  
  // Clear profile cache
  clearProfileCache() {
    console.log('🧹 Clearing profile cache...');
    
    // Find and remove all profile-related items
    Object.keys(localStorage).forEach(key => {
      if (key.includes('docforge_profile_')) {
        localStorage.removeItem(key);
      }
    });
  },
  
  // Force reset everything as a last resort
  async forceReset() {
    console.log('⚠️ Performing force reset...');
    
    try {
      // 1. Sign out
      await supabase.auth.signOut();
      
      // 2. Clear all localStorage
      localStorage.clear();
      
      // 3. Clear all sessionStorage
      sessionStorage.clear();
      
      // 4. Force reload the page
      console.log('✅ Force reset complete - reloading page');
      window.location.reload();
      
      return { repaired: true, action: 'force_reset' };
    } catch (err) {
      console.error('❌ Force reset failed:', err);
      
      // Even if this fails, try to reload
      window.location.reload();
      
      return { repaired: false, error: err.message };
    }
  },
  
  // Add a repair button to the UI
  addRepairButton() {
    // Check if button already exists
    if (document.getElementById('session-repair-btn')) {
      return;
    }
    
    const button = document.createElement('button');
    button.id = 'session-repair-btn';
    button.innerText = 'Repair Session';
    button.style.position = 'fixed';
    button.style.bottom = '20px';
    button.style.left = '20px';
    button.style.padding = '8px 16px';
    button.style.backgroundColor = '#2196F3';
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '4px';
    button.style.cursor = 'pointer';
    button.style.zIndex = '9999';
    button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    
    button.onclick = () => {
      this.checkAndRepair();
    };
    
    document.body.appendChild(button);
  }
};

export default sessionRepair;