/**
 * PWA (Progressive Web App) Utilities
 * 
 * Handles service worker registration, installation prompts,
 * and offline functionality for mobile optimization.
 */

import { createLogger } from './logger.js';

// Create logger for this module
const logger = createLogger('PWA');

/**
 * PWA configuration
 */
export const PWA_CONFIG = {
    SERVICE_WORKER_URL: '/sw.js',
    INSTALL_PROMPT_DELAY: 3000, // 3 seconds
    OFFLINE_CHECK_INTERVAL: 30000, // 30 seconds
    CACHE_STRATEGY: 'stale-while-revalidate'
};

/**
 * PWA Manager class
 */
export class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isOnline = navigator.onLine;
        this.serviceWorkerRegistration = null;
        this.installPromptShown = false;
        
        this.init();
    }

    /**
     * Initialize PWA functionality
     */
    async init() {
        try {
            // Register service worker
            await this.registerServiceWorker();
            
            // Set up install prompt handling
            this.setupInstallPrompt();
            
            // Set up offline/online detection
            this.setupOfflineDetection();
            
            // Check if already installed
            this.checkInstallationStatus();
            
            logger.info('PWA Manager initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize PWA Manager:', error);
        }
    }

    /**
     * Register service worker
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register(
                    PWA_CONFIG.SERVICE_WORKER_URL,
                    { scope: '/' }
                );

                this.serviceWorkerRegistration = registration;

                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    if (newWorker) {
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                // New service worker is available
                                this.showUpdateAvailable();
                            }
                        });
                    }
                });

                logger.info('Service Worker registered successfully');
                return registration;
            } catch (error) {
                logger.error('Service Worker registration failed:', error);
                throw error;
            }
        } else {
            logger.warn('Service Worker not supported');
            return null;
        }
    }

    /**
     * Set up install prompt handling
     */
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (event) => {
            // Prevent the mini-infobar from appearing on mobile
            event.preventDefault();
            
            // Save the event so it can be triggered later
            this.deferredPrompt = event;
            
            // Show custom install prompt after delay
            setTimeout(() => {
                if (!this.installPromptShown && !this.isInstalled) {
                    this.showInstallPrompt();
                }
            }, PWA_CONFIG.INSTALL_PROMPT_DELAY);
        });

        window.addEventListener('appinstalled', () => {
            logger.info('PWA was installed');
            this.isInstalled = true;
            this.deferredPrompt = null;
            this.hideInstallPrompt();
        });
    }

    /**
     * Set up offline/online detection
     */
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showOnlineStatus();
            logger.info('App is back online');
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showOfflineStatus();
            logger.info('App is offline');
        });

        // Periodic connectivity check
        setInterval(() => {
            this.checkConnectivity();
        }, PWA_CONFIG.OFFLINE_CHECK_INTERVAL);
    }

    /**
     * Check if app is already installed
     */
    checkInstallationStatus() {
        // Check if running in standalone mode
        if (window.matchMedia('(display-mode: standalone)').matches) {
            this.isInstalled = true;
            logger.info('App is running in standalone mode');
        }

        // Check for iOS standalone mode
        if (window.navigator.standalone === true) {
            this.isInstalled = true;
            logger.info('App is running in iOS standalone mode');
        }
    }

    /**
     * Show custom install prompt
     */
    showInstallPrompt() {
        if (!this.deferredPrompt || this.installPromptShown) return;

        this.installPromptShown = true;

        // Create custom install prompt UI
        const promptContainer = document.createElement('div');
        promptContainer.className = 'pwa-install-prompt';
        promptContainer.innerHTML = `
            <div class="pwa-install-content">
                <div class="pwa-install-icon">📱</div>
                <div class="pwa-install-text">
                    <h3>Install DocForge AI</h3>
                    <p>Get the full app experience with offline access and faster loading.</p>
                </div>
                <div class="pwa-install-actions">
                    <button class="pwa-install-btn" id="pwa-install-accept">Install</button>
                    <button class="pwa-install-btn secondary" id="pwa-install-dismiss">Not now</button>
                </div>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .pwa-install-prompt {
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                z-index: 10000;
                animation: slideUp 0.3s ease-out;
            }
            
            .pwa-install-content {
                display: flex;
                align-items: center;
                padding: 16px;
                gap: 12px;
            }
            
            .pwa-install-icon {
                font-size: 24px;
            }
            
            .pwa-install-text {
                flex: 1;
            }
            
            .pwa-install-text h3 {
                margin: 0 0 4px 0;
                font-size: 16px;
                font-weight: 600;
            }
            
            .pwa-install-text p {
                margin: 0;
                font-size: 14px;
                color: #666;
            }
            
            .pwa-install-actions {
                display: flex;
                gap: 8px;
            }
            
            .pwa-install-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            
            .pwa-install-btn:not(.secondary) {
                background: #2563eb;
                color: white;
            }
            
            .pwa-install-btn.secondary {
                background: #f3f4f6;
                color: #374151;
            }
            
            @keyframes slideUp {
                from { transform: translateY(100%); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            
            @media (max-width: 640px) {
                .pwa-install-prompt {
                    left: 10px;
                    right: 10px;
                    bottom: 10px;
                }
                
                .pwa-install-content {
                    flex-direction: column;
                    text-align: center;
                }
                
                .pwa-install-actions {
                    width: 100%;
                    justify-content: center;
                }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(promptContainer);

        // Handle install button click
        document.getElementById('pwa-install-accept').addEventListener('click', () => {
            this.installApp();
        });

        // Handle dismiss button click
        document.getElementById('pwa-install-dismiss').addEventListener('click', () => {
            this.hideInstallPrompt();
        });

        // Auto-hide after 10 seconds
        setTimeout(() => {
            this.hideInstallPrompt();
        }, 10000);
    }

    /**
     * Install the PWA
     */
    async installApp() {
        if (!this.deferredPrompt) return;

        try {
            // Show the install prompt
            this.deferredPrompt.prompt();

            // Wait for the user to respond to the prompt
            const { outcome } = await this.deferredPrompt.userChoice;

            if (outcome === 'accepted') {
                logger.info('User accepted the install prompt');
            } else {
                logger.info('User dismissed the install prompt');
            }

            // Clear the deferredPrompt
            this.deferredPrompt = null;
            this.hideInstallPrompt();
        } catch (error) {
            logger.error('Failed to install app:', error);
        }
    }

    /**
     * Hide install prompt
     */
    hideInstallPrompt() {
        const prompt = document.querySelector('.pwa-install-prompt');
        if (prompt) {
            prompt.remove();
        }
    }

    /**
     * Show update available notification
     */
    showUpdateAvailable() {
        // Create update notification
        const notification = document.createElement('div');
        notification.className = 'pwa-update-notification';
        notification.innerHTML = `
            <div class="pwa-update-content">
                <span>A new version is available!</span>
                <button id="pwa-update-reload">Reload</button>
            </div>
        `;

        document.body.appendChild(notification);

        document.getElementById('pwa-update-reload').addEventListener('click', () => {
            window.location.reload();
        });
    }

    /**
     * Show online status
     */
    showOnlineStatus() {
        this.showConnectionStatus('online', '🟢 Back online');
    }

    /**
     * Show offline status
     */
    showOfflineStatus() {
        this.showConnectionStatus('offline', '🔴 You\'re offline');
    }

    /**
     * Show connection status
     */
    showConnectionStatus(type, message) {
        // Remove existing status
        const existing = document.querySelector('.pwa-connection-status');
        if (existing) existing.remove();

        const status = document.createElement('div');
        status.className = `pwa-connection-status ${type}`;
        status.textContent = message;

        document.body.appendChild(status);

        // Auto-hide after 3 seconds
        setTimeout(() => {
            status.remove();
        }, 3000);
    }

    /**
     * Check connectivity
     */
    async checkConnectivity() {
        try {
            const response = await fetch('/manifest.json', {
                method: 'HEAD',
                cache: 'no-cache'
            });
            
            if (response.ok && !this.isOnline) {
                this.isOnline = true;
                this.showOnlineStatus();
            }
        } catch (error) {
            if (this.isOnline) {
                this.isOnline = false;
                this.showOfflineStatus();
            }
        }
    }

    /**
     * Get PWA status
     */
    getStatus() {
        return {
            isInstalled: this.isInstalled,
            isOnline: this.isOnline,
            hasServiceWorker: !!this.serviceWorkerRegistration,
            canInstall: !!this.deferredPrompt
        };
    }
}

// Create singleton instance
export const pwaManager = new PWAManager();

// Export utilities
export default {
    PWAManager,
    pwaManager,
    PWA_CONFIG
};
