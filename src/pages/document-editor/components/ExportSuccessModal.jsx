import React, { useEffect } from 'react';
import Icon from '../../../components/AppIcon';

/**
 * ExportSuccessModal - Professional modal for export success/error notifications
 * Replaces alert() with a modern, branded modal experience
 */
const ExportSuccessModal = ({
  isOpen,
  onClose,
  isSuccess = true,
  documentTitle = 'Document',
  exportFormat = 'PDF',
  fileSize = null,
  message = null,
  onRetry = null,
  onExportAnother = null,
  onDownload = null
}) => {
  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Auto-dismiss after 5 seconds for success (optional)
  useEffect(() => {
    if (isOpen && isSuccess) {
      const timer = setTimeout(() => {
        onClose();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [isOpen, isSuccess, onClose]);

  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getSuccessContent = () => ({
    icon: 'CheckCircle',
    iconColor: 'text-green-500',
    iconBg: 'bg-green-50',
    title: 'Document Successfully Created',
    description: message || `Your ${exportFormat.toUpperCase()} document has been generated and is ready for download.`,
    primaryButton: onDownload ? 'Download' : 'Done',
    primaryAction: onDownload || onClose
  });

  const getErrorContent = () => ({
    icon: 'XCircle',
    iconColor: 'text-red-500',
    iconBg: 'bg-red-50',
    title: 'Export Failed',
    description: message || `Failed to export your document as ${exportFormat.toUpperCase()}. Please try again.`,
    primaryButton: 'Retry',
    primaryAction: onRetry || onClose
  });

  const content = isSuccess ? getSuccessContent() : getErrorContent();

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300"
      onClick={handleBackdropClick}
    >
      <div className="relative w-full max-w-md bg-white rounded-xl shadow-2xl transform transition-all duration-300 scale-100 animate-in fade-in slide-in-from-bottom-4">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <Icon name="X" size={20} />
        </button>

        {/* Modal Content */}
        <div className="p-6">
          {/* Success/Error Icon */}
          <div className="flex justify-center mb-4">
            <div className={`w-16 h-16 rounded-full ${content.iconBg} flex items-center justify-center`}>
              <Icon name={content.icon} size={32} className={content.iconColor} />
            </div>
          </div>

          {/* Title */}
          <h2 className="text-xl font-semibold text-gray-900 text-center mb-2">
            {content.title}
          </h2>

          {/* Document Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Document:</span>
              <span className="font-medium text-gray-900 truncate ml-2" title={documentTitle}>
                {documentTitle}
              </span>
            </div>
            <div className="flex items-center justify-between text-sm mt-2">
              <span className="text-gray-600">Format:</span>
              <span className="font-medium text-gray-900">{exportFormat.toUpperCase()}</span>
            </div>
            {fileSize && (
              <div className="flex items-center justify-between text-sm mt-2">
                <span className="text-gray-600">File Size:</span>
                <span className="font-medium text-gray-900">{fileSize}</span>
              </div>
            )}
          </div>

          {/* Description */}
          <p className="text-gray-600 text-center mb-6 text-sm leading-relaxed">
            {content.description}
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            {/* Primary Action Button */}
            <button
              onClick={content.primaryAction}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                isSuccess
                  ? 'bg-primary hover:bg-primary-dark text-white shadow-sm hover:shadow-md'
                  : 'bg-red-600 hover:bg-red-700 text-white shadow-sm hover:shadow-md'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                {isSuccess && onDownload && <Icon name="Download" size={16} />}
                {!isSuccess && <Icon name="RefreshCw" size={16} />}
                <span>{content.primaryButton}</span>
              </div>
            </button>

            {/* Secondary Actions */}
            <div className="flex space-x-3">
              {/* Export Another Format Button (Success only) */}
              {isSuccess && onExportAnother && (
                <button
                  onClick={onExportAnother}
                  className="flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                >
                  Export Another
                </button>
              )}

              {/* Close/Done Button */}
              <button
                onClick={onClose}
                className={`${isSuccess && onExportAnother ? 'flex-1' : 'w-full'} py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm`}
              >
                {isSuccess ? 'Done' : 'Cancel'}
              </button>
            </div>
          </div>
        </div>

        {/* Auto-dismiss indicator for success */}
        {isSuccess && (
          <div className="px-6 pb-4">
            <div className="text-xs text-gray-500 text-center">
              This dialog will close automatically in 5 seconds
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExportSuccessModal;
