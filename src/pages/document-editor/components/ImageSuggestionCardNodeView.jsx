import React from 'react';
import { NodeViewWrapper } from '@tiptap/react';
import ImageSuggestionCard from './ImageSuggestionCard';

/**
 * Generate placeholder images when no real images are available
 * This provides a fallback for development and testing
 * Uses Unsplash-compatible data structure for consistency
 */
const generatePlaceholderImages = (count, searchQuery) => {
  const placeholderImages = [];

  // Curated high-quality placeholder images from Unsplash
  const unsplashPlaceholders = [
    {
      id: 'unsplash-placeholder-1',
      url: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&h=600&fit=crop',
      thumbnailUrl: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=300&h=200&fit=crop',
      description: `${searchQuery} - Professional workspace`,
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com',
      downloadUrl: '#',
      width: 800,
      height: 600,
      color: '#4A90E2'
    },
    {
      id: 'unsplash-placeholder-2',
      url: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=800&h=600&fit=crop',
      thumbnailUrl: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=300&h=200&fit=crop',
      description: `${searchQuery} - Business meeting`,
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com',
      downloadUrl: '#',
      width: 800,
      height: 600,
      color: '#7B68EE'
    },
    {
      id: 'unsplash-placeholder-3',
      url: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=800&h=600&fit=crop',
      thumbnailUrl: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=300&h=200&fit=crop',
      description: `${searchQuery} - Creative workspace`,
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com',
      downloadUrl: '#',
      width: 800,
      height: 600,
      color: '#FF6B6B'
    },
    {
      id: 'unsplash-placeholder-4',
      url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
      thumbnailUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',
      description: `${searchQuery} - Reading and learning`,
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com',
      downloadUrl: '#',
      width: 800,
      height: 600,
      color: '#4ECDC4'
    },
    {
      id: 'unsplash-placeholder-5',
      url: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop',
      thumbnailUrl: 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=300&h=200&fit=crop',
      description: `${searchQuery} - Technology and innovation`,
      photographer: 'Unsplash',
      photographerUrl: 'https://unsplash.com',
      downloadUrl: '#',
      width: 800,
      height: 600,
      color: '#45B7D1'
    }
  ];

  for (let i = 0; i < Math.min(count, 5); i++) {
    const baseImage = unsplashPlaceholders[i] || unsplashPlaceholders[0];
    placeholderImages.push({
      ...baseImage,
      id: `placeholder-${searchQuery.replace(/\s+/g, '-')}-${i + 1}`,
      description: `${searchQuery} illustration ${i + 1}`
    });
  }

  return placeholderImages;
};

/**
 * ImageSuggestionCardNodeView - React Node View wrapper for Tiptap
 * 
 * This component bridges the gap between Tiptap's node system and React components.
 * It renders the ImageSuggestionCard as a fully interactive React component within
 * the Tiptap editor while maintaining proper editor integration.
 * 
 * Props from Tiptap:
 * - node: The Tiptap node containing attributes and data
 * - editor: The Tiptap editor instance
 * - getPos: Function to get the node's position in the document
 * - updateAttributes: Function to update node attributes
 * - deleteNode: Function to remove this node from the document
 * - selected: Boolean indicating if this node is selected
 */
const ImageSuggestionCardNodeView = (props) => {
  console.log('🚨 ImageSuggestionCardNodeView component created!', {
    propsKeys: Object.keys(props),
    nodeAttrs: props.node?.attrs,
    editorReadOnly: props.editor?.isEditable === false
  });

  // Debug: Log all props to identify any malformed ones
  if (process.env.NODE_ENV === 'development') {
    const expectedProps = ['node', 'editor', 'getPos', 'updateAttributes', 'deleteNode', 'selected'];
    const unexpectedProps = Object.keys(props).filter(key => !expectedProps.includes(key));
    if (unexpectedProps.length > 0) {
      console.warn('🚨 ImageSuggestionCardNodeView received unexpected props:', unexpectedProps, props);
      // Log the actual values of unexpected props
      unexpectedProps.forEach(prop => {
        console.warn(`🚨 Unexpected prop "${prop}":`, props[prop]);
      });
    }
  }

  // Safely extract only the props we need to prevent prop corruption
  const {
    node,
    editor,
    getPos,
    updateAttributes,
    deleteNode,
    selected
  } = props;


  // Extract attributes from the Tiptap node
  const {
    chapterId = '',
    placementId = '',
    searchQuery = '',
    imageCount = 0,
    chapterTitle = '',
    chapterNumber = ''
  } = node.attrs || {};

  // Handle the "View Images" button click (fallback for modal-based selection)
  const handleViewImages = (chapterId, placementId, placement) => {
    // Access the modal handler from editor storage
    const onOpenImageModal = editor?.storage?.imageModal?.onOpen;

    if (onOpenImageModal) {
      onOpenImageModal(chapterId, placementId, placement);
    }
  };

  // Handle direct image selection from inline gallery
  const handleImageSelect = (selectedImage, chapterId, placementId) => {
    // Insert the selected image into the document
    if (editor) {
      try {
        // Insert image at current position
        editor.chain().focus().setImage({
          src: selectedImage.url,
          alt: selectedImage.description || `Image for ${chapterTitle}`,
          class: 'tiptap-image max-w-full h-auto rounded-lg shadow-sm'
        }).run();

        // Remove this suggestion card node after successful insertion
        setTimeout(() => {
          if (deleteNode) {
            deleteNode();
          }
        }, 100);

      } catch (error) {
        console.error('Error inserting image:', error);
      }
    }
  };

  // Handle card close
  const handleClose = () => {
    if (deleteNode) {
      deleteNode();
    }
  };



  // Create placement object for compatibility with existing card component
  const placement = {
    id: placementId,
    chapterId: chapterId,
    contextualHint: `Suggested placement for ${chapterTitle || 'this section'}`
  };

  return (
    <NodeViewWrapper
      className={`image-suggestion-card-node-view ${selected ? 'ProseMirror-selectednode' : ''}`}
      data-node-type="imageSuggestionCard"
      data-chapter-id={chapterId}
      data-placement-id={placementId}
    >
      {/* Render the actual ImageSuggestionCard component */}
      <ImageSuggestionCard
        chapterId={chapterId}
        placementId={placementId}
        imageCount={imageCount}
        searchQuery={searchQuery}
        chapterTitle={chapterTitle}
        placement={placement}
        onViewImages={handleViewImages}
        onImageSelect={handleImageSelect}
        onClose={handleClose}
        images={generatePlaceholderImages(imageCount, searchQuery)}
        useInlineGallery={true}
        className={selected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
      />


    </NodeViewWrapper>
  );
};

export default ImageSuggestionCardNodeView;
