import React, { useEffect, useState, useCallback, useRef } from 'react';
import { EditorContent, useEditor, ReactNodeViewRenderer } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { convertAIContentToHTML, removeImageSuggestionCards } from '../../../utils/contentConverter';
import ImageUrlInput from './ImageUrlInput';
import ContextualImageSelectionModal from './ContextualImageSelectionModal';
import ImageImportModal from './ImageImportModal';
import ContentPreviewModal from '../../../components/modals/ContentPreviewModal.jsx';
import ToastNotification from './ToastNotification.jsx';
import { ImageSuggestionCardExtension } from './ImageSuggestionCardExtension';
import { EnhancedImageExtension } from './EnhancedImageExtension';
import { AIImageGenerationExtension } from './AIImageGenerationExtension';
import useScrollAwarePosition from '../../../hooks/useScrollAwarePosition';
import AppIcon from '../../../components/AppIcon';

/**
 * Icon mapping configuration for floating menu actions
 * Maps menu actions to their corresponding Lucide React icon names
 * 
 * @type {Object<string, string>}
 */
const MENU_ICONS = {
  // Text formatting icons
  heading2: 'Heading2',           // H2 -> Heading2 icon
  heading3: 'Heading3',           // H3 -> Heading3 icon  
  paragraph: 'Type',              // T -> Type icon
  
  // List formatting icons
  bulletList: 'List',             // • -> List icon
  orderedList: 'ListOrdered',     // 1. -> ListOrdered icon
  
  // Content type icons
  quote: 'Quote',                 // " -> Quote icon
  codeBlock: 'Code',              // </> -> Code icon
  inlineCode: 'Code',             // ` -> Code icon (for inline code)
  image: 'Image',                 // 🖼️ -> Image icon
  
  // List manipulation icons
  indent: 'Indent',               // → -> Indent icon
  outdent: 'Outdent',             // ← -> Outdent icon
  
  // Image manipulation icons
  editAlt: 'Edit3',               // ✏️ -> Edit3 icon
  replaceImage: 'RefreshCw',      // 🔄 -> RefreshCw icon
  resizeSmall: 'Maximize2',       // 📏 -> Maximize2 icon
  resizeMedium: 'Maximize2',      // 📐 -> Maximize2 icon  
  resizeLarge: 'Maximize2',       // 📊 -> Maximize2 icon
  resizeFull: 'Maximize2',        // ↔️ -> Maximize2 icon
  deleteImage: 'Trash2',          // 🗑️ -> Trash2 icon
  
  // AI/DocGenerate icons
  docGenerate: 'Bot',             // 🤖 -> Bot icon
  rewrite: 'Sparkles',            // ✨ -> Sparkles icon
  grammar: 'PenTool',             // ✏️ -> PenTool icon (for grammar fixes)
  reduce: 'TrendingDown',         // 📉 -> TrendingDown icon
  expand: 'TrendingUp',           // 📈 -> TrendingUp icon
  generateImage: 'ImagePlus'      // 🎨 -> ImagePlus icon for AI image generation
};

/**
 * DocumentCanvasMinimal - A minimal, clean Tiptap editor component
 *
 * This is a simplified version of DocumentCanvas that provides:
 * - Basic rich text editing with Tiptap
 * - Enhanced placeholder system with "regular text" placeholder
 * - AI content loading and conversion from markdown to HTML
 * - Content change handling and persistence
 * - Clean, centered layout with responsive design
 * - Minimal dependencies and complexity
 * - Foundation for incremental feature additions
 */
const DocumentCanvasMinimal = ({
  content = null,
  onContentChange = null,
  isLoading = false,
  isReadOnly = false,
  imageSuggestions = {},
  onOpenImageModal = null,
  onEditorReady = null
}) => {
  // State for two-stage floating menu
  const [isMenuExpanded, setIsMenuExpanded] = useState(false);
  const [showFloatingMenu, setShowFloatingMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const [expandedMenuPosition, setExpandedMenuPosition] = useState({ top: 0, left: 0 });
  const [preventAutoClose, setPreventAutoClose] = useState(false);

  // State for DocGenerate submenu
  const [showDocGenerateMenu, setShowDocGenerateMenu] = useState(false);
  const [docGenerateMenuPosition, setDocGenerateMenuPosition] = useState({ top: 0, left: 0 });

  // State for DocGenerate loading and error handling
  const [isDocGenerateLoading, setIsDocGenerateLoading] = useState(false);
  const [docGenerateError, setDocGenerateError] = useState(null);
  const [docGenerateAction, setDocGenerateAction] = useState(null); // Track current action

  // State for content preview modal
  const [showContentPreviewModal, setShowContentPreviewModal] = useState(false);
  const [contentPreviewData, setContentPreviewData] = useState({
    text: '',
    operation: 'rewrite',
    title: 'Process Content'
  });

  // State for toast notifications
  const [toast, setToast] = useState(null);

  // State for error recovery
  const [lastFailedAction, setLastFailedAction] = useState(null);

  // State for image URL input
  const [showImageUrlInput, setShowImageUrlInput] = useState(false);
  const [imageUrlInputPosition, setImageUrlInputPosition] = useState({ top: 0, left: 0 });

  // State for new unified image import modal
  const [showImageImportModal, setShowImageImportModal] = useState(false);

  // State for contextual image selection modal
  const [showContextualImageModal, setShowContextualImageModal] = useState(false);
  const [contextualImageContext, setContextualImageContext] = useState(null);

  // State for AI image generation
  const [isImageGenerating, setIsImageGenerating] = useState(false);
  const [imageGenerationProgress, setImageGenerationProgress] = useState(null);
  const [activeGenerationId, setActiveGenerationId] = useState(null);
  const [generatedImageResult, setGeneratedImageResult] = useState(null);
  const [savedCursorPosition, setSavedCursorPosition] = useState(null);

  // Refs for scroll-aware positioning
  const currentTargetElementRef = useRef(null);
  const currentNodePositionRef = useRef(null);
  
  // Refs for managing menu visibility during scroll
  const menuVisibilityTimeoutRef = useRef(null);
  const lastVisibilityStateRef = useRef(true);
  
  // Refs for expanded menu and trigger button click-outside detection
  const expandedMenuRef = useRef(null);
  const triggerButtonRef = useRef(null);
  const docGenerateMenuRef = useRef(null);
  
  // Ref to track if content has been set to prevent infinite loops
  const contentSetRef = useRef(false);
  const lastContentRef = useRef(null);

  // Centralized menu closing function to ensure consistent behavior
  // Note: editor dependency will be added after editor initialization
  const closeExpandedMenu = useCallback((reason = 'unknown', preserveFocus = true) => {
    // Verify menu is actually expanded before closing
    if (!isMenuExpanded) {
      return;
    }
    
    // Update menu state using existing state management
    setIsMenuExpanded(false);
    
    // Close DocGenerate submenu as well
    setShowDocGenerateMenu(false);
    
    // Reset preventAutoClose flag if it was set to ensure clean state
    if (preventAutoClose) {
      setPreventAutoClose(false);
    }
  }, [preventAutoClose, isMenuExpanded]);

  // Function to recalculate menu positions during scroll events
  const recalculateMenuPosition = useCallback((scrollInfo) => {
    if (!currentTargetElementRef.current || !showFloatingMenu) return;

    const element = currentTargetElementRef.current;
    const rect = element.getBoundingClientRect();

    // Handle visibility changes more gracefully with debouncing
    if (scrollInfo.visibilityChanged) {
      // Clear any pending visibility timeout
      if (menuVisibilityTimeoutRef.current) {
        clearTimeout(menuVisibilityTimeoutRef.current);
        menuVisibilityTimeoutRef.current = null;
      }

      if (!scrollInfo.isVisible) {
        // Element went out of view - debounce hiding to prevent flicker during scroll
        lastVisibilityStateRef.current = false;
        
        menuVisibilityTimeoutRef.current = setTimeout(() => {
          // Only hide if still not visible after delay
          if (!lastVisibilityStateRef.current) {
            setShowFloatingMenu(false);
          }
        }, 150); // 150ms delay to prevent flicker during scroll
        return;
      } else {
        // Element came back into view - restore menu immediately
        lastVisibilityStateRef.current = true;
        setShowFloatingMenu(true);
        // Continue with position recalculation below
      }
    }

    // Check for invalid dimensions during normal scroll (not visibility changes)
    if (!scrollInfo.visibilityChanged && (rect.width === 0 || rect.height === 0)) {
      return; // Don't hide menu, just skip this update
    }

    // Additional safety check - ensure element is still in the DOM
    if (!document.contains(element)) {
      setShowFloatingMenu(false);
      currentTargetElementRef.current = null;
      return;
    }

    // Recalculate button position using the same logic as handleSelectionUpdate
    const viewportWidth = window.innerWidth;

    // Calculate positioning similar to Designrr's approach
    let leftOffset;

    if (viewportWidth <= 768) {
      // Mobile: Smaller offset to prevent going off-screen
      leftOffset = 16; // -left-4 equivalent (16px)
    } else if (viewportWidth <= 1024) {
      // Tablet: Medium offset
      leftOffset = 32; // -left-8 equivalent (32px)
    } else {
      // Desktop: Full offset like Designrr (-50px)
      leftOffset = 50;
    }

    // Position to the left of the content block (absolute positioning relative to document)
    const editorContainer = document.querySelector('.tiptap-editor');
    const containerRect = editorContainer ? editorContainer.getBoundingClientRect() : { top: 0, left: 0 };

    const newButtonPosition = {
      top: rect.top - containerRect.top,
      left: rect.left - containerRect.left - leftOffset
    };

    // Calculate new expanded menu position
    const newExpandedPosition = calculateExpandedMenuPosition(rect, newButtonPosition);

    // Update positions
    setMenuPosition(newButtonPosition);
    setExpandedMenuPosition(newExpandedPosition);

  }, [showFloatingMenu]);

  // Helper function to calculate optimal expanded menu position
  const calculateExpandedMenuPosition = (buttonRect, buttonPosition) => {
    const menuWidth = 200; // min-w-[200px]
    const menuHeight = 300; // Estimated height for menu with options
    const spacing = 8; // Gap between button and menu
    const buttonHeight = 32; // button height (32px)
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const scrollY = window.scrollY;

    // Enhanced margins for better mobile experience
    const marginX = Math.max(20, viewportWidth < 768 ? 16 : 20); // Larger margins on mobile
    const marginY = Math.max(20, viewportWidth < 768 ? 16 : 20);

    // Try positioning below the button first (preferred dropdown style)
    const belowPosition = {
      top: buttonPosition.top + buttonHeight + spacing,
      left: buttonPosition.left // Align with button left edge
    };

    // Check if menu fits below and within viewport horizontally
    const fitsBelow = (belowPosition.top + menuHeight) <= (viewportHeight + scrollY - marginY);
    const fitsHorizontally = (belowPosition.left + menuWidth) <= (viewportWidth - marginX);

    if (fitsBelow && fitsHorizontally) {
      return belowPosition;
    }

    // If menu extends beyond right edge, adjust left position while keeping it below
    if (fitsBelow && !fitsHorizontally) {
      const adjustedBelowPosition = {
        top: belowPosition.top,
        left: Math.max(marginX, viewportWidth - menuWidth - marginX) // Position from right edge
      };
      return adjustedBelowPosition;
    }

    // Try positioning above the button if below doesn't fit
    const abovePosition = {
      top: buttonPosition.top - menuHeight - spacing,
      left: buttonPosition.left
    };

    // Check if menu fits above
    const fitsAbove = (abovePosition.top) >= (scrollY + marginY);
    const fitsAboveHorizontally = (abovePosition.left + menuWidth) <= (viewportWidth - marginX);

    if (fitsAbove && fitsAboveHorizontally) {
      return abovePosition;
    }

    // If above fits vertically but not horizontally, adjust left position
    if (fitsAbove && !fitsAboveHorizontally) {
      const adjustedAbovePosition = {
        top: abovePosition.top,
        left: Math.max(marginX, viewportWidth - menuWidth - marginX)
      };
      return adjustedAbovePosition;
    }

    // Try positioning to the right of the button (fallback for very constrained spaces)
    const rightPosition = {
      top: buttonPosition.top,
      left: buttonPosition.left + buttonHeight + spacing
    };

    // Check if menu fits to the right
    const fitsRight = (rightPosition.left + menuWidth) <= (viewportWidth - marginX);

    if (fitsRight) {
      return rightPosition;
    }

    // Try positioning to the left of the button
    const leftPosition = {
      top: buttonPosition.top,
      left: buttonPosition.left - menuWidth - spacing
    };

    // Check if menu fits to the left
    const fitsLeft = leftPosition.left >= marginX;

    if (fitsLeft) {
      return leftPosition;
    }

    // Final fallback: position below but adjust to fit in viewport (emergency positioning)
    const fallbackPosition = {
      top: Math.max(marginY + scrollY, Math.min(belowPosition.top, viewportHeight + scrollY - menuHeight - marginY)),
      left: Math.max(marginX, Math.min(buttonPosition.left, viewportWidth - menuWidth - marginX))
    };

    return fallbackPosition;
  };

  // Initialize scroll-aware positioning hook
  const { updatePosition: updateScrollPosition } = useScrollAwarePosition({
    onPositionUpdate: recalculateMenuPosition,
    targetElement: currentTargetElementRef.current,
    enabled: showFloatingMenu && !isReadOnly,
    throttleMs: 16 // 60fps for smooth updates
  });

  // Click-outside detection for expanded menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      try {
        // Validate event and event.target exist
        if (!event || !event.target) {
          return;
        }

        // Ensure event.target is a valid DOM node
        if (!(event.target instanceof Node)) {
          return;
        }

        // Add null reference protection for expandedMenuRef.current
        if (!expandedMenuRef.current) {
          return;
        }

        // Check if menu is currently expanded before proceeding
        if (!isMenuExpanded) {
          return;
        }

        // Check if click target is outside both the expanded menu AND the trigger button AND DocGenerate submenu
        const isClickInsideMenu = expandedMenuRef.current.contains(event.target);
        const isClickInsideTrigger = triggerButtonRef.current && triggerButtonRef.current.contains(event.target);
        const isClickInsideDocGenerate = docGenerateMenuRef.current && docGenerateMenuRef.current.contains(event.target);
        const isClickOutside = !isClickInsideMenu && !isClickInsideTrigger && !isClickInsideDocGenerate;

        if (isClickOutside) {
          // Close DocGenerate submenu if it's open
          if (showDocGenerateMenu) {
            setShowDocGenerateMenu(false);
            setPreventAutoClose(false); // Reset preventAutoClose when closing DocGenerate submenu
          }
          
          // Only close main menu if preventAutoClose is not set OR DocGenerate submenu was open
          if (!preventAutoClose || showDocGenerateMenu) {
            closeExpandedMenu('click-outside', false);
          }
        }
      } catch (error) {
        // Gracefully handle errors by not closing the menu to avoid unexpected behavior
      }
    };

    // Only add event listeners when expanded menu is open
    if (isMenuExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside); // Mobile support
      
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('touchstart', handleClickOutside);
      };
    }
  }, [isMenuExpanded, preventAutoClose, closeExpandedMenu, showDocGenerateMenu]);

  // Helper function to determine menu type based on current editor state
  const getCurrentMenuType = () => {
    if (!editor) {
      return 'plus';
    }

    const { state } = editor;
    const { selection } = state;
    let currentNode;

    // Check if this is an atomic node selection (like an image)
    if (selection.node && selection.node.isAtom) {
      currentNode = selection.node;
    } else {
      // Regular text selection
      const { $from } = selection;
      currentNode = $from.node();
    }

    // Define supported node types for floating menu
    const supportedNodeTypes = ['paragraph', 'heading', 'listItem', 'blockquote', 'codeBlock', 'image', 'enhancedImage'];

    if (supportedNodeTypes.includes(currentNode.type.name)) {
      // Special handling for atomic nodes (like images) that don't have content
      const isAtomicNode = ['image', 'enhancedImage'].includes(currentNode.type.name);

      if (isAtomicNode) {
        // Atomic nodes should always show ellipsis menu since they have content (the image itself)
        return 'ellipsis';
      }

      // For non-atomic nodes, check if they have content
      const isEmpty = currentNode.content.size === 0;
      const menuType = isEmpty ? 'plus' : 'ellipsis';
      return menuType;
    }

    return 'plus';
  };

  // Helper function to get current node type for menu customization
  const getCurrentNodeType = () => {
    if (!editor) return 'paragraph';

    const { state } = editor;
    const { selection } = state;

    // Check if this is an atomic node selection (like an image)
    if (selection.node && selection.node.isAtom) {
      return selection.node.type.name;
    }

    // Regular text selection
    const { $from } = selection;
    const currentNode = $from.node();
    return currentNode.type.name;
  };

  // Image handlers - show unified image import modal
  const handleShowImageOptions = () => {
    // Prevent image interactions in read-only mode
    if (isReadOnly) {
      return;
    }

    // Always show the new unified image import modal
    setShowImageImportModal(true);
    closeExpandedMenu('image-import-modal-open', true);
  };

  const handleImageInsert = (imageData) => {
    editor.chain().focus().setEnhancedImage({
      src: imageData.src,
      alt: imageData.alt
    }).run();
    setShowImageUrlInput(false);
  };

  const handleImageUrlCancel = () => {
    setShowImageUrlInput(false);
  };

  // Handlers for new unified image import modal
  const handleImageImportModalClose = () => {
    setShowImageImportModal(false);
    // Clear generated result when modal closes to prevent stale state
    setGeneratedImageResult(null);
  };

  const handleImageImportSelect = (imageData) => {
    if (!editor || !imageData) return;

    try {
      // Insert image into editor using enhanced image extension
      editor.chain().focus().setEnhancedImage({
        src: imageData.src,
        alt: imageData.alt || imageData.description || 'Imported image',
        title: imageData.title || imageData.description
      }).run();

      // Close the modal after successful insertion
      setShowImageImportModal(false);
      setGeneratedImageResult(null);

    } catch (error) {
      console.error('❌ Error inserting image:', error);
    }
  };

  // Image-specific menu handlers
  const handleEditImageAltText = () => {
    if (!editor) return;

    const { state } = editor;
    const { $from } = state.selection;
    const currentNode = $from.node();

    if (currentNode.type.name === 'image' || currentNode.type.name === 'enhancedImage') {
      const currentAlt = currentNode.attrs.alt || '';
      const newAlt = prompt('Enter alt text for the image:', currentAlt);

      if (newAlt !== null) { // User didn't cancel
        const nodeType = currentNode.type.name;
        editor.chain().focus().updateAttributes(nodeType, { alt: newAlt }).run();
      }
    }
  };

  const handleReplaceImage = () => {
    if (!editor) return;

    const { state } = editor;
    const { $from } = state.selection;
    const currentNode = $from.node();

    if (currentNode.type.name === 'image' || currentNode.type.name === 'enhancedImage') {
      const currentSrc = currentNode.attrs.src || '';
      const currentAlt = currentNode.attrs.alt || '';
      const newSrc = prompt('Enter new image URL:', currentSrc);

      if (newSrc !== null && newSrc.trim()) { // User didn't cancel and provided URL
        const nodeType = currentNode.type.name;
        editor.chain().focus().updateAttributes(nodeType, {
          src: newSrc.trim(),
          alt: currentAlt // Keep existing alt text
        }).run();
      }
    }
  };

  const handleResizeImage = (size) => {
    if (!editor) return;

    const { state } = editor;
    const { $from } = state.selection;
    const currentNode = $from.node();

    // Check if current node is an enhanced image type
    if (currentNode.type.name === 'enhancedImage') {
      const sizeClasses = {
        small: 'tiptap-image max-w-xs h-auto rounded-lg shadow-sm', // ~320px max
        medium: 'tiptap-image max-w-md h-auto rounded-lg shadow-sm', // ~448px max
        large: 'tiptap-image max-w-2xl h-auto rounded-lg shadow-sm', // ~672px max
        full: 'tiptap-image max-w-full h-auto rounded-lg shadow-sm' // Full width
      };

      const className = sizeClasses[size] || sizeClasses.medium;
      const nodeType = currentNode.type.name;

      editor.chain().focus().updateAttributes(nodeType, {
        class: className
      }).run();

      console.log(`🖼️ Resized image to ${size}`);
    }
  };

  // DocGenerate menu handlers
  const handleDocGenerateClick = (event) => {
    if (!editor) return;

    // Calculate position for DocGenerate submenu
    const buttonRect = event.currentTarget.getBoundingClientRect();
    const editorContainer = document.querySelector('.tiptap-editor');
    const containerRect = editorContainer ? editorContainer.getBoundingClientRect() : { top: 0, left: 0 };

    const submenuPosition = {
      top: buttonRect.top - containerRect.top,
      left: buttonRect.right - containerRect.left + 8 // 8px spacing from the button
    };

    setDocGenerateMenuPosition(submenuPosition);
    setShowDocGenerateMenu(true);
    setPreventAutoClose(true); // Prevent main menu from closing
  };

  const handleDocGenerateAction = async (action) => {
    if (!editor) return;

    try {
      // Import content processor utilities for validation
      const { 
        extractNodeContent, 
        validateEditorState 
      } = await import('../../../utils/contentProcessor.js');

      // Validate editor state
      const editorValidation = validateEditorState(editor);
      if (!editorValidation.valid) {
        setDocGenerateError(editorValidation.error);
        return;
      }

      // Extract content from current context
      const extractionResult = extractNodeContent(editor);
      
      if (extractionResult.isEmpty) {
        setDocGenerateError('No content to process. Please add some text first.');
        return;
      }

      // Prepare content preview modal data
      const titleMap = {
        'rewrite': 'Rewrite Content',
        'fix-grammar': 'Fix Grammar & Spelling',
        'reduce': 'Make Content Concise',
        'expand': 'Expand Content'
      };

      setContentPreviewData({
        text: extractionResult.text,
        operation: action,
        title: titleMap[action] || 'Process Content'
      });
      
      setShowContentPreviewModal(true);
      setShowDocGenerateMenu(false); // Close the DocGenerate menu
      
    } catch (error) {
      console.error('DocGenerate preview error:', error);
      setDocGenerateError(error.message || 'Failed to prepare content preview');
    }
  };

  // Retry function for failed operations
  const handleRetryDocGenerate = () => {
    if (lastFailedAction) {
      setDocGenerateError(null);
      handleDocGenerateAction(lastFailedAction);
    }
  };



  // Handler for canceling image generation
  const handleCancelImageGeneration = async () => {
    if (!activeGenerationId) return;

    try {
      console.log('🚫 Canceling image generation', { generationId: activeGenerationId });

      // Cancel using extension if available
      if (editor.commands.cancelImageGeneration) {
        editor.commands.cancelImageGeneration(activeGenerationId);
      }

      // Try to cancel via Gemini Image API
      const { cancelGeneration } = await import('../../../services/geminiImageService');
      await cancelGeneration(activeGenerationId);

      // Reset state
      setIsImageGenerating(false);
      setActiveGenerationId(null);
      setImageGenerationProgress(null);

      // Show cancellation toast
      setToast({
        message: 'Image generation canceled',
        type: 'info'
      });

    } catch (error) {
      console.error('Failed to cancel image generation:', error);
      // Still reset state even if cancellation failed
      setIsImageGenerating(false);
      setActiveGenerationId(null);
      setImageGenerationProgress(null);
    }
  };

  const handleContentPreviewConfirm = async () => {
    const action = contentPreviewData.operation;

    if (!editor || isDocGenerateLoading) return;

    try {
      setIsDocGenerateLoading(true);
      setDocGenerateError(null);
      setDocGenerateAction(action);
      // Keep modal open to show loading state

      // Import AI service and content processor utilities
      const { 
        rewriteContent, 
        fixGrammar, 
        reduceContent, 
        expandContent,
        DocGenerateError 
      } = await import('../../../services/aiService.js');
      
      const { 
        extractNodeContent, 
        replaceNodeContent, 
        getDocumentContext,
        analyzeContentChanges,
        validateEditorState 
      } = await import('../../../utils/contentProcessor.js');

      // Validate editor state
      const editorValidation = validateEditorState(editor);
      if (!editorValidation.valid) {
        throw new Error(editorValidation.error);
      }

      // Extract content from current context
      const extractionResult = extractNodeContent(editor);
      
      if (extractionResult.isEmpty) {
        throw new Error('No content to process. Please add some text first.');
      }

      // Get context for better AI results
      const context = {
        nodeType: extractionResult.nodeType,
        documentContext: extractionResult.documentContext,
        selectionLength: extractionResult.text.length
      };

      console.log(`🤖 DocGenerate ${action} starting...`, {
        text: extractionResult.text.substring(0, 100) + '...',
        nodeType: extractionResult.nodeType,
        hasSelection: extractionResult.hasSelection
      });

      let result;
      
      // Handle different actions
      switch (action) {
        case 'rewrite':
          result = await rewriteContent(extractionResult.text, context);
          break;
          
        case 'fix-grammar':
          result = await fixGrammar(extractionResult.text, context);
          break;
          
        case 'reduce':
          result = await reduceContent(extractionResult.text, context);
          break;
          
        case 'expand':
          result = await expandContent(extractionResult.text, context);
          break;
          
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      if (!result || !result.trim()) {
        throw new Error('AI service returned empty result');
      }

      // Analyze changes before applying
      const changes = analyzeContentChanges(extractionResult.text, result);
      console.log(`� Content changes analysis:`, changes);

      // Replace content in editor
      replaceNodeContent(editor, result, extractionResult, {
        preserveFormatting: true,
        focusAfter: true
      });

      // Success feedback
      console.log(`✅ DocGenerate ${action} completed successfully`);

      // Show success toast
      const actionLabels = {
        'rewrite': 'Content rewritten',
        'fix-grammar': 'Grammar fixed',
        'reduce': 'Content made concise',
        'expand': 'Content expanded'
      };
      setToast({
        message: actionLabels[action] || 'Content processed successfully',
        type: 'success'
      });

      // Close modal on success
      setShowContentPreviewModal(false);

    } catch (error) {
      console.error(`❌ DocGenerate ${action} failed:`, error);
      
      let errorMessage = 'An unexpected error occurred';
      
      if (error.name === 'DocGenerateError') {
        errorMessage = error.message;
      } else if (error.message?.includes('API key')) {
        errorMessage = 'AI service not configured. Please check your API settings.';
      } else if (error.message?.includes('quota') || error.message?.includes('limit')) {
        errorMessage = 'AI service quota exceeded. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setDocGenerateError(errorMessage);
      setLastFailedAction(action); // Store failed action for retry

      // Show error toast with retry option
      setToast({
        message: `${errorMessage} - Check the menu for retry option.`,
        type: 'error'
      });

      // Clear error after 10 seconds (longer to allow retry)
      setTimeout(() => {
        setDocGenerateError(null);
        setLastFailedAction(null);
      }, 10000);
    } finally {
      setIsDocGenerateLoading(false);
      setDocGenerateAction(null);
      setShowContentPreviewModal(false);
      setShowDocGenerateMenu(false);
      setIsMenuExpanded(false);
      setPreventAutoClose(false);
    }
  };

  // Contextual image suggestion card handlers
  const handleContextualImageCardClick = (chapterId, placementId, placement) => {
    setContextualImageContext({
      chapterId,
      placementId,
      placement
    });
    setShowContextualImageModal(true);
  };

  const handleContextualImageSelect = async (selectedImage, placement, chapterId, placementId) => {
    if (!editor || !selectedImage) {
      console.error('Missing editor or selectedImage:', { editor: !!editor, selectedImage: !!selectedImage });
      return;
    }

    try {
      // Simple direct insertion using enhanced image
      const success = editor.chain().focus().setEnhancedImage({
        src: selectedImage.url || selectedImage.thumbnailUrl,
        alt: selectedImage.description || `Image for ${chapterId}`,
        title: selectedImage.description || 'AI suggested image'
      }).run();

      if (success) {
        // Try to find and remove the card after successful insertion
        setTimeout(() => {
          const { state } = editor;
          const { doc } = state;
          
          let cardPosition = null;
          let cardNode = null;
          
          doc.descendants((node, pos) => {
            if (node.type.name === 'imageSuggestionCard' && 
                node.attrs.chapterId === chapterId && 
                node.attrs.placementId === placementId) {
              cardPosition = pos;
              cardNode = node;
              return false; // Stop traversal
            }
          });

          if (cardPosition !== null && cardNode) {
            console.log('🗑️ Found card to remove, removing it...');
            const tr = state.tr.delete(cardPosition, cardPosition + cardNode.nodeSize);
            editor.view.dispatch(tr);
            console.log('✅ Card removed successfully');
          } else {
            console.log('⚠️ Card not found for removal');
          }
        }, 500);
      }

    } catch (error) {
      console.error('❌ Error inserting contextual image:', error);
      alert('Error inserting image: ' + error.message);
    }
  };

  const handleContextualImageModalClose = () => {
    setShowContextualImageModal(false);
    setContextualImageContext(null);
  };

  // Handler for AI image generation
  const handleImageGeneration = async (prompt, options) => {
    if (!editor) {
      console.error('Editor not available for image generation');
      return;
    }

    // Save the current cursor position before starting generation
    const currentSelection = editor.state.selection;
    const cursorPosition = {
      from: currentSelection.from,
      to: currentSelection.to,
      anchor: currentSelection.anchor,
      head: currentSelection.head
    };

    setSavedCursorPosition(cursorPosition);

    // Generate unique ID for this generation
    const generationId = `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      console.log('🎨 Starting AI image generation', { generationId, prompt: prompt.substring(0, 50), options });

      // Set generation state
      setIsImageGenerating(true);
      setActiveGenerationId(generationId);
      setImageGenerationProgress({
        status: 'starting',
        progress: 0,
        elapsed: 0
      });

      // Use AI Image Generation Extension to track the generation
      if (editor.commands.startImageGeneration) {
        editor.commands.startImageGeneration({
          prompt,
          style: options.style,
          size: options.size,
          onProgress: (progress) => {
            setImageGenerationProgress(progress);
          }
        });
      }

      // Import the Gemini Image service
      const { generateImage } = await import('../../../services/geminiImageService');

      // Start generation with progress tracking
      const result = await generateImage(prompt, options, (progress) => {
        console.log('🔄 Generation progress:', progress);
        setImageGenerationProgress({
          status: progress.status || 'processing',
          progress: progress.progress || 0,
          elapsed: progress.elapsed || 0
        });
      });

      if (result.success && result.imageUrl) {
        console.log('🖼️ Preloading generated image for instant preview...');

        // Preload the image to eliminate loading delay (with environment check)
        if (typeof window === 'undefined' || !window.Image) {
          console.warn('⚠️ Image constructor not available, showing preview immediately');
          // Fallback: show preview immediately without preloading
          setImageGenerationProgress({
            status: 'completed',
            progress: 1,
            elapsed: Date.now() - parseInt(generationId.split('_')[1])
          });

          setGeneratedImageResult({
            ...result,
            prompt,
            options,
            generationId,
            preloaded: false
          });

          setIsImageGenerating(false);
          setActiveGenerationId(null);

          setToast({
            message: 'Image generated! Review and insert when ready.',
            type: 'success'
          });

          return; // Exit early
        }

        let preloadImage;
        try {
          preloadImage = new Image();
        } catch (error) {
          console.warn('⚠️ Failed to create Image object:', error);
          // Fallback: show preview immediately without preloading
          setImageGenerationProgress({
            status: 'completed',
            progress: 1,
            elapsed: Date.now() - parseInt(generationId.split('_')[1])
          });

          setGeneratedImageResult({
            ...result,
            prompt,
            options,
            generationId,
            preloaded: false
          });

          setIsImageGenerating(false);
          setActiveGenerationId(null);

          setToast({
            message: 'Image generated! Review and insert when ready.',
            type: 'success'
          });

          return; // Exit early
        }
        preloadImage.onload = () => {
          // Check if result has already been set to avoid duplicate processing
          if (generatedImageResult && generatedImageResult.generationId === generationId) {
            return;
          }

          // Update progress to completion and set result simultaneously
          setImageGenerationProgress({
            status: 'completed',
            progress: 1,
            elapsed: Date.now() - parseInt(generationId.split('_')[1])
          });

          // Store result for preview with preloaded image
          setGeneratedImageResult({
            ...result,
            prompt,
            options,
            generationId,
            preloaded: true
          });

          // Reset generation state immediately since we have the result
          setIsImageGenerating(false);

          // Clear activeGenerationId after a short delay to ensure state is stable
          setTimeout(() => {
            setActiveGenerationId(null);
          }, 100);

          // Show success toast
          setToast({
            message: 'Image generated! Review and insert when ready.',
            type: 'success'
          });
        };

        preloadImage.onerror = () => {
          // Check if result has already been set to avoid duplicate processing
          if (generatedImageResult && generatedImageResult.generationId === generationId) {
            return;
          }

          // Still show the preview even if preload fails
          setImageGenerationProgress({
            status: 'completed',
            progress: 1,
            elapsed: Date.now() - parseInt(generationId.split('_')[1])
          });

          setGeneratedImageResult({
            ...result,
            prompt,
            options,
            generationId,
            preloaded: false
          });

          setIsImageGenerating(false);

          // Clear activeGenerationId after a short delay to ensure state is stable
          setTimeout(() => {
            setActiveGenerationId(null);
          }, 100);

          setToast({
            message: 'Image generated! Review and insert when ready.',
            type: 'success'
          });
        };

        // Start preloading
        preloadImage.src = result.imageUrl;

        // Set a timeout fallback in case preloading takes too long
        const timeoutId = setTimeout(() => {
          if (!generatedImageResult || generatedImageResult.generationId !== generationId) {
            console.warn('⏰ Image preload timeout, showing preview anyway');
            // Manually trigger the success flow
            setImageGenerationProgress({
              status: 'completed',
              progress: 1,
              elapsed: Date.now() - parseInt(generationId.split('_')[1])
            });

            setGeneratedImageResult({
              ...result,
              prompt,
              options,
              generationId,
              preloaded: false // Mark as not preloaded due to timeout
            });

            setIsImageGenerating(false);

            // Clear activeGenerationId after a short delay to ensure state is stable
            setTimeout(() => {
              setActiveGenerationId(null);
            }, 100);

            setToast({
              message: 'Image generated! Review and insert when ready.',
              type: 'success'
            });
          }
        }, 2000); // 2 second timeout

        // Clear timeout if preload completes successfully
        const originalOnLoad = preloadImage.onload;
        preloadImage.onload = () => {
          clearTimeout(timeoutId);
          originalOnLoad();
        };

        const originalOnError = preloadImage.onerror;
        preloadImage.onerror = () => {
          clearTimeout(timeoutId);
          originalOnError();
        };

      } else {
        throw new Error('Image generation failed or returned no image');
      }

    } catch (error) {
      console.error('❌ AI image generation failed:', error);

      // Handle generation error using extension if available
      if (editor.commands.handleGenerationError) {
        editor.commands.handleGenerationError(generationId, error);
      }

      // Categorize error and provide appropriate user message
      let userMessage = 'Image generation failed. Please try again.';
      let canRetry = true;

      if (error.message.includes('API key')) {
        userMessage = 'API key not configured. Check your settings.';
        canRetry = false;
      } else if (error.message.includes('rate limit')) {
        userMessage = 'Rate limit reached. Please wait a moment and try again.';
        canRetry = true;
      } else if (error.message.includes('timeout')) {
        userMessage = 'Generation timed out. Try a simpler prompt.';
        canRetry = true;
      } else if (error.message.includes('content policy')) {
        userMessage = 'Content not allowed. Please modify your prompt.';
        canRetry = true;
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        userMessage = 'Network error. Check your connection and try again.';
        canRetry = true;
      }

      // Show error toast with retry option if applicable
      setToast({
        message: userMessage,
        type: 'error',
        action: canRetry ? {
          label: 'Retry',
          onClick: () => handleImageGeneration(prompt, options)
        } : null
      });
    } finally {
      // Always reset generation state in case of errors
      // Give preload handlers time to execute before clearing activeGenerationId
      setTimeout(() => {
        setIsImageGenerating(false);
        setActiveGenerationId(null);
        // Only clear progress if no result was generated
        if (!generatedImageResult) {
          setImageGenerationProgress(null);
        }
      }, 3000); // Increased timeout to allow preload handlers to execute
    }
  };

  // Handler for inserting approved generated image
  const handleInsertGeneratedImage = async (result) => {
    if (!editor || !result) {
      console.error('Editor or result not available for image insertion');
      return;
    }

    try {
      console.log('📝 Inserting approved generated image', {
        hasEditor: !!editor,
        hasImageUrl: !!result.imageUrl,
        resultKeys: Object.keys(result),
        isEditable: editor.isEditable,
        isFocused: editor.isFocused,
        hasSelection: !!editor.state.selection
      });

      // Ensure editor is in edit mode and focused
      if (!editor.isEditable) {
        throw new Error('Editor is not in edit mode');
      }

      // Restore the saved cursor position if available
      if (savedCursorPosition) {
        console.log('🎯 Restoring cursor position:', savedCursorPosition);

        try {
          // Create a text selection at the saved position
          const { state, dispatch } = editor.view;
          const tr = state.tr.setSelection(
            state.doc.resolve(savedCursorPosition.from).textSelection ||
            state.doc.resolve(savedCursorPosition.from)
          );
          dispatch(tr);

          // Focus the editor
          editor.commands.focus();

          // Give it a moment to process the selection
          await new Promise(resolve => setTimeout(resolve, 50));
        } catch (selectionError) {
          console.warn('Failed to restore exact cursor position, using fallback:', selectionError);
          // Fallback to focusing at the saved position
          editor.commands.focus(savedCursorPosition.from);
        }
      } else {
        console.log('⚠️ No saved cursor position, focusing editor');
        // Ensure editor is focused
        if (!editor.isFocused) {
          editor.commands.focus();
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        // If no selection, move cursor to end of document
        if (!editor.state.selection || editor.state.selection.empty) {
          editor.commands.focus('end');
        }
      }

      // Try multiple insertion methods to ensure compatibility
      let success = false;
      let insertionMethod = 'unknown';

      // Method 1: Chain command (most common pattern)
      try {
        success = editor.chain().focus().setEnhancedImage({
          src: result.imageUrl,
          alt: result.prompt ? `AI generated: ${result.prompt.substring(0, 100)}` : 'AI generated image',
          title: result.options?.style ? `Generated with ${result.options.style} style` : 'AI generated image',
          // 🔥 CRITICAL: Add AI-generated attribute for export detection
          'data-ai-generated': 'true',
          'data-prompt': result.prompt || '',
          'data-style': result.options?.style || '',
          'data-generation-id': `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }).run();
        insertionMethod = 'chain';
      } catch (error) {
        console.warn('Chain method failed:', error.message);
      }

      // Method 2: Direct command if chain failed
      if (!success && editor.commands.setEnhancedImage) {
        try {
          success = editor.commands.setEnhancedImage({
            src: result.imageUrl,
            alt: result.prompt ? `AI generated: ${result.prompt.substring(0, 100)}` : 'AI generated image',
            title: result.options?.style ? `Generated with ${result.options.style} style` : 'AI generated image',
            // 🔥 CRITICAL: Add AI-generated attribute for export detection
            'data-ai-generated': 'true',
            'data-prompt': result.prompt || '',
            'data-style': result.options?.style || '',
            'data-generation-id': `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          });
          insertionMethod = 'direct';
        } catch (error) {
          console.warn('Direct method failed:', error.message);
        }
      }

      // Method 3: Use the same pattern as handleImageInsert
      if (!success) {
        try {
          editor.chain().focus().setEnhancedImage({
            src: result.imageUrl,
            alt: result.prompt ? `AI generated: ${result.prompt.substring(0, 100)}` : 'AI generated image',
            // 🔥 CRITICAL: Add AI-generated attribute for export detection
            'data-ai-generated': 'true',
            'data-prompt': result.prompt || '',
            'data-style': result.options?.style || ''
          }).run();
          success = true; // Assume success if no error thrown
          insertionMethod = 'simplified';
        } catch (error) {
          console.warn('Simplified method failed:', error.message);
        }
      }

      console.log('🔍 Image insertion result:', {
        success,
        insertionMethod,
        hasEnhancedImageCommand: !!editor.commands.setEnhancedImage,
        editorState: {
          isEditable: editor.isEditable,
          isFocused: editor.isFocused,
          hasContent: editor.getText().length > 0
        }
      });

      if (success) {
        console.log(`✅ Generated image inserted successfully using ${insertionMethod} method`);

        // Clear the result, cursor position, and close modal
        setGeneratedImageResult(null);
        setSavedCursorPosition(null);
        setShowAIImageGenerationModal(false);

        // Show success toast
        setToast({
          message: 'Image inserted into document!',
          type: 'success'
        });
      } else {
        throw new Error(`All insertion methods failed. Editor state: editable=${editor.isEditable}, focused=${editor.isFocused}`);
      }

    } catch (error) {
      console.error('❌ Failed to insert generated image:', error);

      // Show error toast with more specific information
      setToast({
        message: `Failed to insert image: ${error.message}. Try clicking in the document first.`,
        type: 'error'
      });
    }
  };



  // Helper function to render node-specific menu options
  const renderNodeSpecificMenuOptions = (nodeType, isEmptyNode) => {
    if (isEmptyNode) {
      // Plus menu options - same for all node types
      return (
        <>
          <button
            onClick={() => {
              editor.chain().focus().toggleHeading({ level: 2 }).run();
              closeExpandedMenu('menu-action', false); // Don't preserve focus since editor.chain().focus() already called
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            aria-label="Add heading level 2"
          >
            <AppIcon 
              name={MENU_ICONS.heading2} 
              size="sm" 
              className="mr-3" 
              aria-hidden="true" 
            />
            <span className="text-gray-600">Heading</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleHeading({ level: 3 }).run();
              closeExpandedMenu('menu-action', false); // Don't preserve focus since editor.chain().focus() already called
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            aria-label="Add heading level 3"
          >
            <AppIcon 
              name={MENU_ICONS.heading3} 
              size="sm" 
              className="mr-3" 
              aria-hidden="true" 
            />
            <span className="text-gray-600">Heading</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().setParagraph().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            aria-label="Add regular text paragraph"
          >
            <AppIcon 
              name={MENU_ICONS.paragraph} 
              size="sm" 
              className="mr-3" 
              aria-hidden="true" 
            />
            <span className="text-gray-600">Regular text</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleBulletList().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            aria-label="Add bulleted list"
          >
            <AppIcon 
              name={MENU_ICONS.bulletList} 
              size="sm" 
              className="mr-3" 
              aria-hidden="true" 
            />
            <span className="text-gray-600">Bulleted list</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleOrderedList().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            aria-label="Add ordered list"
          >
            <AppIcon 
              name={MENU_ICONS.orderedList} 
              size="sm" 
              className="mr-3" 
              aria-hidden="true" 
            />
            <span className="text-gray-600">Ordered list</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleBlockquote().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            aria-label="Add quote block"
          >
            <AppIcon 
              name={MENU_ICONS.quote} 
              size="sm" 
              className="mr-3" 
              aria-hidden="true" 
            />
            <span className="text-gray-600">Quote</span>
          </button>

          <button
            onClick={() => {
              editor.chain().focus().toggleCodeBlock().run();
              setIsMenuExpanded(false);
            }}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            aria-label="Add code block"
          >
            <AppIcon 
              name={MENU_ICONS.codeBlock} 
              size="sm" 
              className="mr-3" 
              aria-hidden="true" 
            />
            <span className="text-gray-600">Code</span>
          </button>

          <button
            onClick={handleShowImageOptions}
            className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
            aria-label="Add image to document"
          >
            <AppIcon
              name={MENU_ICONS.image}
              size="sm"
              className="mr-3"
              aria-hidden="true"
            />
            <span className="text-gray-600">
              Image
            </span>
          </button>


        </>
      );
    }

    // Ellipsis menu options - different for each node type
    switch (nodeType) {
      case 'listItem':
        return (
          <>
            <button
              onClick={handleDocGenerateClick}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="AI content generation options"
              aria-haspopup="menu"
            >
              <AppIcon 
                name={MENU_ICONS.docGenerate} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">DocGenerate</span>
              <span className="ml-auto text-xs text-gray-400" aria-hidden="true">▶</span>
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            <button
              onClick={() => {
                editor.chain().focus().setParagraph().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert list item to paragraph"
            >
              <AppIcon 
                name={MENU_ICONS.paragraph} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to paragraph</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleBulletList().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert to bullet list"
            >
              <AppIcon 
                name={MENU_ICONS.bulletList} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to bullet list</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleOrderedList().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert to ordered list"
            >
              <AppIcon 
                name={MENU_ICONS.orderedList} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to ordered list</span>
            </button>

            <button
              onClick={() => {
                // Indent list item (if possible)
                editor.chain().focus().sinkListItem('listItem').run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Indent list item"
            >
              <AppIcon 
                name={MENU_ICONS.indent} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Indent</span>
            </button>

            <button
              onClick={() => {
                // Outdent list item (if possible)
                editor.chain().focus().liftListItem('listItem').run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Outdent list item"
            >
              <AppIcon 
                name={MENU_ICONS.outdent} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Outdent</span>
            </button>
          </>
        );

      case 'blockquote':
        return (
          <>
            <button
              onClick={() => {
                editor.chain().focus().setParagraph().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert blockquote to paragraph"
            >
              <AppIcon 
                name={MENU_ICONS.paragraph} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to paragraph</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleHeading({ level: 2 }).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert blockquote to heading level 2"
            >
              <AppIcon 
                name={MENU_ICONS.heading2} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to Heading 2</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleHeading({ level: 3 }).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert blockquote to heading level 3"
            >
              <AppIcon 
                name={MENU_ICONS.heading3} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to Heading 3</span>
            </button>
          </>
        );

      case 'codeBlock':
        return (
          <>
            <button
              onClick={handleDocGenerateClick}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="AI content generation options"
              aria-haspopup="menu"
            >
              <AppIcon 
                name={MENU_ICONS.docGenerate} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">DocGenerate</span>
              <span className="ml-auto text-xs text-gray-400" aria-hidden="true">▶</span>
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            <button
              onClick={() => {
                editor.chain().focus().setParagraph().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert code block to paragraph"
            >
              <AppIcon 
                name={MENU_ICONS.paragraph} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to paragraph</span>
            </button>

            <button
              onClick={() => {
                // Convert to inline code (simplified approach)
                const content = editor.getHTML();
                const textContent = editor.getText();
                editor.chain().focus().setParagraph().run();
                editor.chain().focus().insertContent(`<code>${textContent}</code>`).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert code block to inline code"
            >
              <AppIcon 
                name={MENU_ICONS.inlineCode} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to inline code</span>
            </button>
          </>
        );

      case 'image':
      case 'enhancedImage':
        return (
          <>
            <button
              onClick={() => {
                handleEditImageAltText();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Edit image alt text"
            >
              <AppIcon 
                name={MENU_ICONS.editAlt} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Edit alt text</span>
            </button>

            <button
              onClick={() => {
                handleReplaceImage();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Replace image with new image"
            >
              <AppIcon 
                name={MENU_ICONS.replaceImage} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Replace image</span>
            </button>

            <button
              onClick={() => {
                handleResizeImage('small');
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Resize image to small size"
            >
              <AppIcon 
                name={MENU_ICONS.resizeSmall} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Resize to small</span>
            </button>

            <button
              onClick={() => {
                handleResizeImage('medium');
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Resize image to medium size"
            >
              <AppIcon 
                name={MENU_ICONS.resizeMedium} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Resize to medium</span>
            </button>

            <button
              onClick={() => {
                handleResizeImage('large');
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Resize image to large size"
            >
              <AppIcon 
                name={MENU_ICONS.resizeLarge} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Resize to large</span>
            </button>

            <button
              onClick={() => {
                handleResizeImage('full');
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Resize image to full width"
            >
              <AppIcon 
                name={MENU_ICONS.resizeFull} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Full width</span>
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            <button
              onClick={() => {
                editor.chain().focus().deleteSelection().run();
                setIsMenuExpanded(false);
                
                // Show success notification
                setTimeout(() => {
                  console.log('✅ Image deleted successfully. Press Ctrl+Z to undo.');
                }, 100);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-red-50 hover:text-red-700 rounded flex items-center text-red-600 transition-colors duration-200"
              aria-label="Delete image permanently"
            >
              <AppIcon 
                name={MENU_ICONS.deleteImage} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="font-medium">Delete image</span>
              <span className="ml-auto text-xs text-gray-400" aria-hidden="true">Del</span>
            </button>
          </>
        );

      default:
        // Default ellipsis menu for paragraphs and headings
        return (
          <>
            <button
              onClick={handleDocGenerateClick}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="AI content generation options"
              aria-haspopup="menu"
            >
              <AppIcon 
                name={MENU_ICONS.docGenerate} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">DocGenerate</span>
              <span className="ml-auto text-xs text-gray-400" aria-hidden="true">▶</span>
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            <button
              onClick={() => {
                editor.chain().focus().toggleHeading({ level: 2 }).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert to heading level 2"
            >
              <AppIcon 
                name={MENU_ICONS.heading2} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to Heading 2</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleHeading({ level: 3 }).run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert to heading level 3"
            >
              <AppIcon 
                name={MENU_ICONS.heading3} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to Heading 3</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().setParagraph().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert to paragraph"
            >
              <AppIcon 
                name={MENU_ICONS.paragraph} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to paragraph</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleBulletList().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert to bullet list"
            >
              <AppIcon 
                name={MENU_ICONS.bulletList} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to bullet list</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleBlockquote().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert to quote block"
            >
              <AppIcon 
                name={MENU_ICONS.quote} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to quote</span>
            </button>

            <button
              onClick={() => {
                editor.chain().focus().toggleCodeBlock().run();
                setIsMenuExpanded(false);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              aria-label="Convert to code block"
            >
              <AppIcon 
                name={MENU_ICONS.codeBlock} 
                size="sm" 
                className="mr-3" 
                aria-hidden="true" 
              />
              <span className="text-gray-600">Convert to code</span>
            </button>
          </>
        );
    }
  };

  // Convert AI content to HTML for initial editor content
  const getInitialContent = () => {
    if (content) {
      const htmlContent = convertAIContentToHTML(content, imageSuggestions, isReadOnly);
      return htmlContent;
    }
    return '<p></p>'; // Empty paragraph to show placeholder
  };

  // Initialize Tiptap editor with enhanced configuration
  const editor = useEditor({
    extensions: [
      // CRITICAL FIX: Configure StarterKit to exclude Image extension to prevent conflicts
      // StarterKit includes a built-in Image extension that conflicts with our EnhancedImageExtension
      StarterKit.configure({
        // Exclude the built-in Image extension to prevent conflicts with EnhancedImageExtension
        image: false,
      }),
      Placeholder.configure({
        placeholder: isReadOnly ? '' : 'regular text',
        showOnlyWhenEditable: true,
        showOnlyCurrent: false,
      }),
      // Use enhanced image extension with hover delete functionality
      // This handles ALL images (new AI images + existing content parsing)
      // Now this will be the ONLY extension handling img tags
      EnhancedImageExtension,
      // Always include the ImageSuggestionCardExtension to prevent parsing issues
      // when switching between edit and read-only modes
      ImageSuggestionCardExtension,
      // AI Image Generation Extension for handling generated images
      AIImageGenerationExtension,
    ],
    content: getInitialContent(),
    editable: !isReadOnly,
    onCreate: ({ editor }) => {
      // Set up editor storage for image suggestion card communication
      editor.storage.imageModal = {
        onOpen: onOpenImageModal || handleContextualImageCardClick,
        onContextual: handleContextualImageCardClick  // Direct access to contextual modal
      };
      
      // Call onEditorReady callback to expose editor instance to parent component
      if (onEditorReady) {
        onEditorReady(editor);
      }
    },
    onUpdate: ({ editor }) => {
      // Call content change handler if provided (only when editable)
      if (onContentChange && !isReadOnly) {
        const html = editor.getHTML();
        console.log('Editor content updated, calling onContentChange');
        onContentChange(html);
      }
    },
    editorProps: {
      attributes: {
        class: `prose prose-base max-w-none w-full focus:outline-none transition-all duration-200 hover:prose-headings:text-blue-700 ${isReadOnly ? 'cursor-default' : ''}`,
        spellcheck: isReadOnly ? 'false' : 'true',
        'data-testid': 'tiptap-editor',
        'data-readonly': isReadOnly ? 'true' : 'false',
      },
    },
  });

  // Add keyboard shortcuts for enhanced image deletion
  useEffect(() => {
    if (!editor || isReadOnly) return;

    const handleKeyDown = (event) => {
      // Check if we have an active image selection
      const { state } = editor;
      const { $from } = state.selection;
      const currentNode = $from.node();

      if (currentNode.type.name === 'image' || currentNode.type.name === 'enhancedImage') {
        if (event.key === 'Delete' || event.key === 'Backspace') {
          event.preventDefault();
          
          // Delete the image
          editor.chain().focus().deleteSelection().run();
          
          // Show success feedback
          setTimeout(() => {
            console.log('✅ Image deleted via keyboard shortcut. Press Ctrl+Z to undo.');
          }, 100);
          
          return true;
        }
      }
    };

    // Add event listener to the editor element
    const editorElement = editor.view.dom;
    editorElement.addEventListener('keydown', handleKeyDown);

    return () => {
      editorElement.removeEventListener('keydown', handleKeyDown);
    };
  }, [editor, isReadOnly]);

  // Process image suggestion placeholders and style them
  const processImageSuggestionPlaceholders = useCallback(() => {
    if (!editor) return;

    // Find all paragraphs that contain our placeholder text
    const editorElement = editor.view.dom;
    const paragraphs = editorElement.querySelectorAll('p');

    paragraphs.forEach(p => {
      const text = p.textContent || '';
      if (text.includes('IMAGE_SUGGESTION_PLACEHOLDER_')) {
        // Extract chapter ID from the text
        const match = text.match(/IMAGE_SUGGESTION_PLACEHOLDER_([^_]+)_(\d+)_(.+)/);
        if (match) {
          const [, chapterId, imageCount, searchQuery] = match;
          const cleanSearchQuery = searchQuery.replace(/_/g, ' ').replace(/🖼️/g, '').trim();

          // Style the paragraph as an image suggestion card
          p.style.cssText = `
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px dashed #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin: 24px 0;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
          `;

          // Add data attributes for event handling
          p.setAttribute('data-image-suggestion', chapterId);
          p.setAttribute('data-search-query', cleanSearchQuery);
          p.setAttribute('data-image-count', imageCount);
          p.className = 'image-suggestion-styled';

          // Replace the text content with styled content
          p.innerHTML = `
            🖼️ <strong>Image suggestions available</strong><br>
            <span style="color: #1e40af; font-size: 14px;">
              ${imageCount} images found for "${cleanSearchQuery}" • Click to view
            </span>
          `;
        }
      }
    });
  }, [editor]);

  // Update editor content when content prop changes
  useEffect(() => {
    if (editor && content) {
      // CRITICAL FIX: Only update editor content for initial AI content loading
      // Skip setContent() calls when user has made edits (prevents cursor jumping)
      if (!content.editorHTML) {
        // Check if content has changed to prevent unnecessary updates
        const contentString = JSON.stringify(content);
        if (lastContentRef.current === contentString && contentSetRef.current) {
          return; // Content hasn't changed, skip update
        }

        // CRITICAL FIX: In read-only mode, strip any existing image card HTML to prevent prop corruption
        let newHTML = convertAIContentToHTML(content, imageSuggestions, isReadOnly);
        if (isReadOnly) {
          // Remove any image suggestion card HTML that might cause React prop errors
          newHTML = newHTML.replace(/<div[^>]*data-type="image-suggestion-card"[^>]*><\/div>/g, '');
        }

        // CRITICAL FIX: Prevent infinite loop by temporarily disabling onUpdate callback
        const originalOnUpdate = editor.options.onUpdate;
        editor.options.onUpdate = null;
        
        editor.commands.setContent(newHTML);
        
        // Mark content as set and store current content
        contentSetRef.current = true;
        lastContentRef.current = contentString;
        
        // Restore onUpdate callback after content is set
        editor.options.onUpdate = originalOnUpdate;

        // Process placeholders after content is set (only in edit mode)
        if (!isReadOnly) {
          setTimeout(() => {
            processImageSuggestionPlaceholders();
          }, 100);
        }
      }
    }
  }, [editor, content, imageSuggestions, isReadOnly, processImageSuggestionPlaceholders]);



  // Focus restoration effect - handles focus when menu closes
  useEffect(() => {
    // This effect runs when isMenuExpanded changes from true to false
    // We restore focus to maintain cursor position after menu interactions
    if (!isMenuExpanded && editor && !editor.isFocused) {
      try {
        editor.commands.focus();
      } catch (error) {
        console.warn('Could not restore editor focus:', error);
      }
    }
  }, [isMenuExpanded, editor]);

  // Cleanup effect for menu visibility timeout
  useEffect(() => {
    return () => {
      // Clear timeout on component unmount or when showFloatingMenu changes
      if (menuVisibilityTimeoutRef.current) {
        clearTimeout(menuVisibilityTimeoutRef.current);
        menuVisibilityTimeoutRef.current = null;
      }
    };
  }, [showFloatingMenu]);

  // Handle click outside to close URL input
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showImageUrlInput) {
        // Check if click is outside the URL input component
        const urlInputElement = event.target.closest('.fixed.z-50');
        if (!urlInputElement) {
          setShowImageUrlInput(false);
        }
      }
    };

    if (showImageUrlInput) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showImageUrlInput]);

  // Clean up interactive states when switching to read-only mode
  useEffect(() => {
    if (isReadOnly && editor) {

      // Reset all floating menu states
      setShowFloatingMenu(false);
      closeExpandedMenu('read-only-mode', false); // Don't preserve focus in read-only mode

      // Reset all image-related states
      setShowImageUrlInput(false);
      setShowImageImportModal(false);
      setShowContextualImageModal(false);
      setContextualImageContext(null);

      // Clean up any image suggestion card nodes to prevent text injection
      const content = editor.getHTML();
      const cleanedContent = removeImageSuggestionCards(content);

      // Only update if content changed to avoid infinite loops
      if (cleanedContent !== content) {
        console.log('🧹 Cleaned up image suggestion cards for read-only mode');
        editor.commands.setContent(cleanedContent, false); // false = don't emit updates
      }
    }
  }, [isReadOnly, editor]);

  // Handle clicks on image suggestion placeholders
  useEffect(() => {
    const handlePlaceholderClick = (event) => {
      // Check if click is on a styled image suggestion placeholder
      const placeholder = event.target.closest('.image-suggestion-styled');

      if (placeholder) {
        event.preventDefault();
        event.stopPropagation();

        const chapterId = placeholder.dataset.imageSuggestion;
        const searchQuery = placeholder.dataset.searchQuery;
        const imageCount = parseInt(placeholder.dataset.imageCount) || 0;

        console.log('🎯 Image suggestion placeholder clicked:', {
          chapterId,
          searchQuery,
          imageCount
        });

        // Find the chapter data from imageSuggestions (simplified approach)
        const chapterData = imageSuggestions[chapterId];

        if (chapterData && chapterData.images?.length > 0) {
          // Create a simple placement object for chapter-boundary placement
          const placement = {
            id: 'chapter-boundary',
            type: 'chapter-boundary',
            position: 'before-chapter',
            description: chapterData.description || `Image suggestions for ${chapterData.chapterTitle}`,
            contextualHint: chapterData.contextualHint || `Visual content for ${chapterData.chapterTitle}`
          };

          handleContextualImageCardClick(chapterId, 'chapter-boundary', placement);
        } else {
          console.warn('Could not find chapter data or images for placeholder:', { chapterId });
        }
      }
    };

    // Add event listener to document
    document.addEventListener('click', handlePlaceholderClick);

    return () => {
      document.removeEventListener('click', handlePlaceholderClick);
    };
  }, [imageSuggestions]); // Re-run when imageSuggestions change

  // Handle menu positioning and visibility with useCallback to prevent stale closures
  const handleSelectionUpdate = useCallback(() => {
    if (!editor) return;

    const updateId = Math.random().toString(36).substr(2, 9);

    const { state } = editor;
    const { selection } = state;

    // For atomic nodes (like images), we need to check if the selection is a NodeSelection
    let currentNode;
    let isAtomicNodeSelected = false;

    if (selection.node && selection.node.isAtom) {
      // This is a NodeSelection on an atomic node (like an image)
      currentNode = selection.node;
      isAtomicNodeSelected = true;
    } else {
      // Regular text selection - get the containing node
      const { $from } = selection;
      currentNode = $from.node();
      console.log('📝 Text node selected:', currentNode.type.name);
    }

    // Show menu for all supported node types
    const supportedNodeTypes = ['paragraph', 'heading', 'listItem', 'blockquote', 'codeBlock', 'image', 'enhancedImage'];

    // Get the DOM element for positioning - declare in broader scope
    const { view } = editor;
    let element;

    if (supportedNodeTypes.includes(currentNode.type.name)) {
      if (isAtomicNodeSelected) {
        // For atomic nodes (like images), get the DOM element directly from the selection
        const pos = selection.from;
        const dom = view.domAtPos(pos);
        element = dom.node;

        // If we got a text node, find its parent element
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // For atomic nodes, look for the specific element or its wrapper
        while (element && !['IMG', 'DIV'].includes(element.tagName)) {
          element = element.parentElement;
        }
      } else {
        // For regular text nodes, use the existing logic
        const { $from } = selection;
        const start = $from.start();
        const dom = view.domAtPos(start);
        element = dom.node;

        // If we got a text node, find its parent element
        if (element.nodeType === Node.TEXT_NODE) {
          element = element.parentElement;
        }

        // Find the actual content block by looking for the paragraph/heading element
        while (element && !['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'LI', 'BLOCKQUOTE', 'PRE', 'DIV'].includes(element.tagName)) {
          element = element.parentElement;
        }

        console.log('📝 Found text node element:', element?.tagName, element);
      }

      if (element) {
        const rect = element.getBoundingClientRect();



        // Store element reference for scroll-aware positioning
        currentTargetElementRef.current = element;
        currentNodePositionRef.current = isAtomicNodeSelected ? selection.from : selection.$from.pos;

        // Responsive positioning logic
        const viewportWidth = window.innerWidth;
        const availableLeftSpace = rect.left;
        const buttonWidth = 32; // w-8 = 32px
        const minOffset = 10; // Minimum space from content

        // Calculate positioning similar to Designrr's approach
        // Use negative left positioning relative to the content block
        let leftOffset;

        if (viewportWidth <= 768) {
          // Mobile: Smaller offset to prevent going off-screen
          leftOffset = 16; // -left-4 equivalent (16px)
        } else if (viewportWidth <= 1024) {
          // Tablet: Medium offset
          leftOffset = 32; // -left-8 equivalent (32px)
        } else {
          // Desktop: Full offset like Designrr (-50px)
          leftOffset = 50;
        }

        // Position to the left of the content block (absolute positioning relative to document)
        // For absolute positioning, we need to account for the parent container's position
        const editorContainer = document.querySelector('.tiptap-editor');
        const containerRect = editorContainer ? editorContainer.getBoundingClientRect() : { top: 0, left: 0 };

        const buttonPosition = {
          top: rect.top - containerRect.top,
          left: rect.left - containerRect.left - leftOffset
        };





        // Calculate expanded menu position using smart positioning
        const expandedPosition = calculateExpandedMenuPosition(rect, buttonPosition);

        setMenuPosition(buttonPosition);
        setExpandedMenuPosition(expandedPosition);
        setShowFloatingMenu(true);
      }
    } else {
      console.log('❌ Hiding floating menu:', {
        nodeType: currentNode.type.name,
        isSupported: supportedNodeTypes.includes(currentNode.type.name),
        supportedNodeTypes,
        isAtomicNodeSelected,
        element: element?.tagName
      });
      setShowFloatingMenu(false);
      // Clear element reference when menu is hidden
      currentTargetElementRef.current = null;
      currentNodePositionRef.current = null;
    }

    // Close expanded menu when selection changes (but not immediately after opening)

    if (isMenuExpanded && !preventAutoClose) {
      closeExpandedMenu('selection-change', false); // Focus will be handled by the focus restoration effect
    } else if (preventAutoClose) {
      setPreventAutoClose(false); // Reset the flag after one cycle
    }
  }, [editor, isMenuExpanded, preventAutoClose]); // Include all dependencies to prevent stale closures

  const handleUpdate = useCallback(() => {
    // Close expanded menu when editor content changes
    if (isMenuExpanded) {
      closeExpandedMenu('content-update', false); // Don't preserve focus since content is being updated
    }
  }, [isMenuExpanded, closeExpandedMenu]); // Include closeExpandedMenu dependency

  useEffect(() => {
    if (!editor) return;

    // Listen for editor updates and selection changes
    editor.on('update', handleUpdate);
    editor.on('selectionUpdate', handleSelectionUpdate);

    // REMOVED: Initial check that was interfering with cursor positioning
    // handleSelectionUpdate(); // This was causing cursor to jump after Enter key

    return () => {
      editor.off('update', handleUpdate);
      editor.off('selectionUpdate', handleSelectionUpdate);
    };
  }, [editor, handleUpdate, handleSelectionUpdate]); // Include callback dependencies

  return (
    <div className="h-full bg-gradient-to-br from-gray-50 to-gray-100 overflow-auto">
      {/* Main Content Container */}
      <div className="min-h-full py-6 sm:py-8 px-4">
        {/* Document Canvas */}
        <div className="max-w-4xl mx-auto">
          {/* Document Paper */}
          <div className="bg-white shadow-lg hover:shadow-xl rounded-lg min-h-[800px] p-6 sm:p-8 transition-all duration-300 border border-gray-200 hover:border-blue-200 focus-within:border-blue-400 focus-within:shadow-xl focus-within:shadow-blue-100/50">
            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center min-h-[700px] animate-pulse">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-3 border-blue-600 mx-auto mb-6 shadow-lg"></div>
                  <p className="text-gray-600 text-lg font-medium">Loading content...</p>
                  <p className="text-gray-400 text-sm mt-2">Converting AI-generated content</p>
                </div>
              </div>
            )}



            {/* Editor Content with enhanced styling */}
            {!isLoading && (
              <div className="relative">
                <EditorContent
                  editor={editor}
                  className="min-h-[700px] focus:outline-none tiptap-editor transition-all duration-200"
                  onClick={(e) => {
                    console.log('🖱️ EDITOR CLICK EVENT:', {
                      target: e.target.tagName,
                      targetClass: e.target.className,
                      clientX: e.clientX,
                      clientY: e.clientY,
                      timestamp: new Date().toISOString(),
                      isReadOnly
                    });

                    // Prevent floating menu interactions in read-only mode
                    if (isReadOnly) {
                      console.log('🔒 Read-only mode: Skipping floating menu logic');
                      return;
                    }
                  }}
                />

                {/* Custom Floating Menu for Both Empty and Content Blocks */}
                {editor && showFloatingMenu && !isReadOnly && (() => {
                  const menuType = getCurrentMenuType();
                  const nodeType = getCurrentNodeType();
                  console.log('🎯 Rendering floating menu:', {
                    showFloatingMenu,
                    isReadOnly,
                    menuType,
                    nodeType,
                    menuPosition
                  });
                  return (
                    <>
                      {/* Always show the trigger button */}
                      <div
                        ref={triggerButtonRef}
                        className="absolute z-50 flex justify-center items-center -my-1.5"
                        style={{
                          top: `${menuPosition.top}px`,
                          left: `${menuPosition.left}px`,
                        }}
                      >
                        {menuType === 'plus' ? (
                          <button
                            onClick={(e) => {
                              // Prevent interactions in read-only mode
                              if (isReadOnly) {
                                console.log('🔒 Read-only mode: Plus button click ignored');
                                return;
                              }

                              e.preventDefault();
                              e.stopPropagation();

                              // Toggle behavior: if menu is already expanded, close it
                              if (isMenuExpanded) {
                                closeExpandedMenu('button-toggle', false);
                              } else {
                                setPreventAutoClose(true);
                                setIsMenuExpanded(true);

                                // Reset preventAutoClose after a brief delay to allow click-outside detection
                                setTimeout(() => {
                                  setPreventAutoClose(false);
                                }, 100);
                              }
                            }}
                            className="p-2 flex shrink-0 gap-2 hover:bg-gray-50 items-center justify-center rounded-lg transition-colors whitespace-nowrap border border-gray-200 shadow-lg bg-white text-blue-600 select-none"
                            title="Add content"
                            aria-label="Add content to document"
                            aria-expanded={isMenuExpanded}
                            aria-haspopup="menu"
                          >
                            <span className="text-blue-600 text-lg font-bold leading-none" aria-hidden="true">+</span>
                          </button>
                        ) : (
                          <button
                            onClick={(e) => {
                              // Prevent interactions in read-only mode
                              if (isReadOnly) {
                                console.log('🔒 Read-only mode: Ellipsis button click ignored');
                                return;
                              }

                              e.preventDefault();
                              e.stopPropagation();

                              // Toggle behavior: if menu is already expanded, close it
                              if (isMenuExpanded) {
                                closeExpandedMenu('button-toggle', false);
                              } else {
                                setPreventAutoClose(true);
                                setIsMenuExpanded(true);

                                // Reset preventAutoClose after a brief delay to allow click-outside detection
                                setTimeout(() => {
                                  setPreventAutoClose(false);
                                }, 100);
                              }
                            }}
                            className="p-2 flex shrink-0 gap-2 hover:bg-gray-50 items-center justify-center rounded-lg transition-colors whitespace-nowrap border border-gray-200 shadow-lg bg-white text-gray-600 select-none"
                            title="Edit content"
                            aria-label="Edit content options"
                            aria-expanded={isMenuExpanded}
                            aria-haspopup="menu"
                          >
                            <span className="text-gray-600 text-sm font-bold leading-none" aria-hidden="true">⋯</span>
                          </button>
                        )}
                      </div>

                      {/* Show expanded menu when isMenuExpanded is true */}
                      {isMenuExpanded && (
                        <div
                          ref={expandedMenuRef}
                          className="absolute z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-2 min-w-[200px]"
                          style={{
                            top: `${expandedMenuPosition.top}px`,
                            left: `${expandedMenuPosition.left}px`,
                          }}
                        >
                          {/* Header with Plus or Ellipsis Icon */}
                          <div className="flex items-center mb-2 pb-2 border-b border-gray-100">
                            {getCurrentMenuType() === 'plus' ? (
                              <>
                                <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center mr-2">
                                  <span className="text-white text-sm font-bold">+</span>
                                </div>
                                <span className="text-sm text-gray-600 font-medium">Add content</span>
                              </>
                            ) : (
                              <>
                                <div className="w-6 h-6 bg-gray-600 rounded flex items-center justify-center mr-2">
                                  <span className="text-white text-xs font-bold">⋯</span>
                                </div>
                                <span className="text-sm text-gray-600 font-medium">Edit content</span>
                              </>
                            )}
                          </div>

                          {/* Menu Options */}
                          <div className="space-y-1">
                            {renderNodeSpecificMenuOptions(getCurrentNodeType(), getCurrentMenuType() === 'plus')}
                          </div>
                        </div>
                      )}

                      {/* DocGenerate Submenu */}
                      {showDocGenerateMenu && (
                        <div
                          ref={docGenerateMenuRef}
                          className="absolute z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-[240px]"
                          style={{
                            top: `${docGenerateMenuPosition.top}px`,
                            left: `${docGenerateMenuPosition.left}px`,
                          }}
                        >
                          {/* Header */}
                          <div className="pb-2 mb-2 border-b border-gray-100">
                            <div className="flex items-center space-x-2">
                              <div className="w-6 h-6 bg-blue-500 rounded-md flex items-center justify-center">
                                <span className="text-white text-xs">✨</span>
                              </div>
                              <span className="text-sm font-medium text-gray-900">AI Content Tools</span>
                            </div>
                            {isDocGenerateLoading && (
                              <div className="mt-2 text-xs text-blue-600 flex items-center space-x-1">
                                <div className="animate-spin rounded-full h-3 w-3 border-b border-blue-600"></div>
                                <span>Processing...</span>
                              </div>
                            )}
                          </div>

                          {/* Error Display */}
                          {docGenerateError && (
                            <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-md">
                              <div className="flex items-start space-x-2 mb-2">
                                <div className="w-4 h-4 text-red-500 mt-0.5">
                                  <svg fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                <p className="text-xs text-red-700 flex-1">{docGenerateError}</p>
                              </div>
                              {lastFailedAction && (
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={handleRetryDocGenerate}
                                    disabled={isDocGenerateLoading}
                                    className={`text-xs px-2 py-1 rounded border transition-colors ${
                                      isDocGenerateLoading
                                        ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                                        : 'bg-red-600 text-white border-red-600 hover:bg-red-700'
                                    }`}
                                  >
                                    {isDocGenerateLoading ? 'Retrying...' : 'Try Again'}
                                  </button>
                                  <button
                                    onClick={() => {
                                      setDocGenerateError(null);
                                      setLastFailedAction(null);
                                    }}
                                    className="text-xs text-red-600 hover:text-red-800"
                                  >
                                    Dismiss
                                  </button>
                                </div>
                              )}
                            </div>
                          )}

                          {/* DocGenerate options */}
                          <div className="space-y-1">
                            <button
                              onClick={() => handleDocGenerateAction('rewrite')}
                              disabled={isDocGenerateLoading}
                              aria-label="Rewrite content using AI"
                              className={`w-full text-left px-3 py-2 text-sm rounded flex items-center transition-colors ${
                                isDocGenerateLoading 
                                  ? 'opacity-50 cursor-not-allowed bg-gray-100' 
                                  : 'hover:bg-gray-50'
                              }`}
                            >
                              <span className="mr-3">
                                {isDocGenerateLoading && docGenerateAction === 'rewrite' ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-600"></div>
                                ) : (
                                  <AppIcon 
                                    name={MENU_ICONS.rewrite} 
                                    size="sm" 
                                    aria-hidden="true" 
                                  />
                                )}
                              </span>
                              <span className="text-gray-600">Rewrite</span>
                            </button>

                            <button
                              onClick={() => handleDocGenerateAction('fix-grammar')}
                              disabled={isDocGenerateLoading}
                              aria-label="Fix grammar and spelling using AI"
                              className={`w-full text-left px-3 py-2 text-sm rounded flex items-center transition-colors ${
                                isDocGenerateLoading 
                                  ? 'opacity-50 cursor-not-allowed bg-gray-100' 
                                  : 'hover:bg-gray-50'
                              }`}
                            >
                              <span className="mr-3">
                                {isDocGenerateLoading && docGenerateAction === 'fix-grammar' ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-600"></div>
                                ) : (
                                  <AppIcon 
                                    name={MENU_ICONS.grammar} 
                                    size="sm" 
                                    aria-hidden="true" 
                                  />
                                )}
                              </span>
                              <span className="text-gray-600">Fix spelling & grammar</span>
                            </button>

                            <button
                              onClick={() => handleDocGenerateAction('reduce')}
                              disabled={isDocGenerateLoading}
                              aria-label="Make content more concise using AI"
                              className={`w-full text-left px-3 py-2 text-sm rounded flex items-center transition-colors ${
                                isDocGenerateLoading 
                                  ? 'opacity-50 cursor-not-allowed bg-gray-100' 
                                  : 'hover:bg-gray-50'
                              }`}
                            >
                              <span className="mr-3">
                                {isDocGenerateLoading && docGenerateAction === 'reduce' ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-600"></div>
                                ) : (
                                  <AppIcon 
                                    name={MENU_ICONS.reduce} 
                                    size="sm" 
                                    aria-hidden="true" 
                                  />
                                )}
                              </span>
                              <span className="text-gray-600">Reduce</span>
                            </button>

                            <button
                              onClick={() => handleDocGenerateAction('expand')}
                              disabled={isDocGenerateLoading}
                              aria-label="Expand content with more details using AI"
                              className={`w-full text-left px-3 py-2 text-sm rounded flex items-center transition-colors ${
                                isDocGenerateLoading
                                  ? 'opacity-50 cursor-not-allowed bg-gray-100'
                                  : 'hover:bg-gray-50'
                              }`}
                            >
                              <span className="mr-3">
                                {isDocGenerateLoading && docGenerateAction === 'expand' ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-600"></div>
                                ) : (
                                  <AppIcon
                                    name={MENU_ICONS.expand}
                                    size="sm"
                                    aria-hidden="true"
                                  />
                                )}
                              </span>
                              <span className="text-gray-600">Expand</span>
                            </button>
                          </div>
                        </div>
                      )}
                    </>
                  );
                })()}

                {/* Subtle visual indicator for editor focus */}
                <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-400 to-blue-600 rounded-full opacity-0 transition-opacity duration-300 focus-within:opacity-100 -ml-4"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Image URL Input Component - Only show in edit mode */}
      {!isReadOnly && showImageUrlInput && (
        <div
          className="fixed z-50"
          style={{
            top: `${imageUrlInputPosition.top}px`,
            left: `${imageUrlInputPosition.left}px`,
          }}
        >
          <ImageUrlInput
            isVisible={showImageUrlInput}
            onImageInsert={handleImageInsert}
            onCancel={handleImageUrlCancel}
          />
        </div>
      )}

      {/* Contextual Image Selection Modal - Only show in edit mode */}
      {!isReadOnly && (
        <ContextualImageSelectionModal
          isOpen={showContextualImageModal}
          onClose={handleContextualImageModalClose}
          onImageSelect={handleContextualImageSelect}
          chapterId={contextualImageContext?.chapterId}
          placementId={contextualImageContext?.placementId}
          imageSuggestions={imageSuggestions}
          placement={contextualImageContext?.placement}
        />
      )}

      {/* Content Preview Modal */}
      <ContentPreviewModal
        isOpen={showContentPreviewModal}
        onClose={() => setShowContentPreviewModal(false)}
        onConfirm={handleContentPreviewConfirm}
        currentText={contentPreviewData.text}
        operation={contentPreviewData.operation}
        title={contentPreviewData.title}
        isLoading={isDocGenerateLoading}
      />

      {/* Unified Image Import Modal - Only show in edit mode */}
      {!isReadOnly && (
        <ImageImportModal
          isOpen={showImageImportModal}
          onClose={handleImageImportModalClose}
          onImageSelect={handleImageImportSelect}
          onGenerateImage={handleImageGeneration}
          imageSuggestions={imageSuggestions}
          chapterId={null} // Let modal handle chapter selection internally
          isReviewMode={isReadOnly}
          isGenerating={isImageGenerating}
          generationProgress={imageGenerationProgress}
          generatedResult={generatedImageResult}
        />
      )}



      {/* Enhanced CSS for placeholder styling and editor improvements */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Placeholder styling */
          .tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
            content: attr(data-placeholder);
            float: left;
            color: #9ca3af;
            pointer-events: none;
            height: 0;
            font-style: italic;
            opacity: 0.8;
            transition: opacity 0.2s ease;
          }

          .tiptap-editor .ProseMirror p.is-empty::before {
            content: attr(data-placeholder);
            float: left;
            color: #9ca3af;
            pointer-events: none;
            height: 0;
            font-style: italic;
            opacity: 0.8;
            transition: opacity 0.2s ease;
          }

          /* Enhanced editor styling */
          .tiptap-editor .ProseMirror {
            outline: none;
            line-height: 1.75;
            letter-spacing: 0.025em;
          }

          /* Improved focus states for headings */
          .tiptap-editor .ProseMirror h1:hover,
          .tiptap-editor .ProseMirror h2:hover,
          .tiptap-editor .ProseMirror h3:hover {
            color: #2563eb;
            transition: color 0.2s ease;
          }

          /* Better list styling */
          .tiptap-editor .ProseMirror ul,
          .tiptap-editor .ProseMirror ol {
            padding-left: 1.5rem;
          }

          /* Responsive text sizing */
          @media (max-width: 640px) {
            .tiptap-editor .ProseMirror {
              font-size: 16px;
              line-height: 1.6;
            }
          }

          /* Selection styling */
          .tiptap-editor .ProseMirror ::selection {
            background-color: #dbeafe;
            color: #1e40af;
          }

          /* Prevent default image selection styling to avoid double outlines */
          .tiptap-editor .ProseMirror img.ProseMirror-selectednode,
          .tiptap-editor .ProseMirror .ProseMirror-selectednode img {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
          }

          /* Enhanced image node selection override */
          .enhanced-image-wrapper.ProseMirror-selectednode {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
          }

          /* Floating Menu Styling */
          .floating-menu-container {
            z-index: 50;
          }

          /* Responsive floating menu adjustments */
          @media (max-width: 768px) {
            .floating-menu-container {
              /* Mobile: Smaller buttons and closer positioning */
            }
          }

          @media (min-width: 769px) and (max-width: 1024px) {
            .floating-menu-container {
              /* Tablet/Laptop: Adjusted positioning to prevent overlap */
            }
          }

          @media (min-width: 1025px) {
            .floating-menu-container {
              /* Desktop: Full positioning freedom */
            }
          }

          .floating-menu-container .tippy-box {
            background: transparent;
            border: none;
            box-shadow: none;
          }

          .floating-menu-container .tippy-content {
            padding: 0;
          }

          /* Plus button styling */
          .floating-menu-container button:first-child {
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
          }

          .floating-menu-container button:first-child:hover {
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
          }

          /* Hover effects for menu items */
          .floating-menu-container button:hover {
            background-color: #f9fafb;
            transform: translateX(2px);
            transition: all 0.15s ease;
          }

          /* Active states for menu items */
          .floating-menu-container button:active {
            background-color: #f3f4f6;
            transform: translateX(1px);
          }

          /* Expanded menu animation */
          .floating-menu-container > div {
            animation: menuSlideIn 0.2s ease-out;
          }

          @keyframes menuSlideIn {
            from {
              opacity: 0;
              transform: translateX(-10px) scale(0.95);
            }
            to {
              opacity: 1;
              transform: translateX(0) scale(1);
            }
          }
        `
      }} />

      {/* Toast Notifications */}
      {toast && (
        <ToastNotification
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default DocumentCanvasMinimal;
