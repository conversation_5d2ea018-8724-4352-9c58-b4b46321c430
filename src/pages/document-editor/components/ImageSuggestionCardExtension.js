import { Node } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import ImageSuggestionCardNodeView from './ImageSuggestionCardNodeView';

/**
 * Custom Tiptap extension for Image Suggestion Cards
 *
 * This extension renders image suggestion cards as interactive React components
 * within the Tiptap editor using ReactNodeViewRenderer. This ensures full
 * interactivity and proper integration with the existing modal system.
 *
 * Updated to use React Node Views instead of static HTML rendering.
 */
export const ImageSuggestionCardExtension = Node.create({
  name: 'imageSuggestionCard',

  // Define this as a block-level element
  group: 'block',

  // Make this a leaf node (no content allowed inside)
  // This prevents Tiptap from trying to parse the internal HTML structure
  atom: true,

  // Define the HTML structure this extension should parse
  parseHTML() {
    return [
      {
        tag: 'div[data-type="image-suggestion-card"]',
        getAttrs: (element) => {
          console.log('🚨 ImageSuggestionCardExtension.parseHTML triggered!', {
            elementTag: element.tagName,
            elementAttributes: Array.from(element.attributes).map(attr => `${attr.name}="${attr.value}"`),
            elementHTML: element.outerHTML
          });

          // CRITICAL FIX: Prevent parsing in read-only contexts to avoid prop corruption
          // This is a safety net in case image card HTML somehow makes it through
          const editorElement = element.closest('.ProseMirror');
          const isReadOnly = editorElement && editorElement.getAttribute('contenteditable') === 'false';

          console.log('🔍 parseHTML context check:', {
            hasEditorElement: !!editorElement,
            contentEditable: editorElement?.getAttribute('contenteditable'),
            isReadOnly
          });

          if (isReadOnly) {
            console.log('🔒 BLOCKED: Skipping image card parsing in read-only mode to prevent prop corruption');
            return false; // Don't parse this element
          }

          console.log('✅ ALLOWING: Parsing image card in edit mode');
          return {
            chapterId: element.getAttribute('data-chapter-id'),
            placementId: element.getAttribute('data-placement-id'),
            searchQuery: element.getAttribute('data-search-query'),
            imageCount: parseInt(element.getAttribute('data-image-count')) || 0,
            chapterTitle: element.getAttribute('data-chapter-title'),
            chapterNumber: element.getAttribute('data-chapter-number'),
          };
        },
      },
    ];
  },

  // Use React Node View for interactive rendering
  addNodeView() {
    return ReactNodeViewRenderer(ImageSuggestionCardNodeView);
  },

  // Fallback HTML rendering for non-interactive contexts (e.g., export)
  renderHTML({ node, HTMLAttributes }) {

    const {
      chapterId = '',
      placementId = '',
      searchQuery = '',
      imageCount = 0,
      chapterTitle = '',
      chapterNumber = ''
    } = node.attrs || {};

    // Filter HTMLAttributes to prevent prop corruption
    const safeHTMLAttributes = {};
    if (HTMLAttributes) {
      Object.keys(HTMLAttributes).forEach(key => {
        // Only allow safe HTML attributes and exclude problematic ones
        if (key === 'class' || key === 'className') {
          safeHTMLAttributes.class = HTMLAttributes[key];
        } else if (key.startsWith('data-') || key.startsWith('aria-')) {
          safeHTMLAttributes[key] = HTMLAttributes[key];
        } else if (['id', 'style', 'title'].includes(key)) {
          safeHTMLAttributes[key] = HTMLAttributes[key];
        }
        // Exclude any other potentially problematic attributes
      });
    }

    return [
      'div',
      {
        'data-type': 'image-suggestion-card',
        'data-chapter-id': chapterId,
        'data-placement-id': placementId,
        'data-search-query': searchQuery,
        'data-image-count': imageCount.toString(),
        'data-chapter-title': chapterTitle,
        'data-chapter-number': chapterNumber,
        class: 'image-suggestion-card my-4',
        ...safeHTMLAttributes,
      },
      [
        'div',
        { class: 'p-4 bg-gray-100 border border-gray-300 rounded-lg text-center' },
        `Image suggestions available: ${imageCount} images for "${searchQuery}" (Interactive mode disabled)`
      ]
    ];
  },

  // Add custom attributes for the card data
  addAttributes() {
    return {
      chapterId: {
        default: null,
        parseHTML: element => element.getAttribute('data-chapter-id'),
        renderHTML: attributes => {
          if (!attributes.chapterId) {
            return {};
          }
          return {
            'data-chapter-id': attributes.chapterId,
          };
        },
      },
      placementId: {
        default: null,
        parseHTML: element => element.getAttribute('data-placement-id'),
        renderHTML: attributes => {
          if (!attributes.placementId) {
            return {};
          }
          return {
            'data-placement-id': attributes.placementId,
          };
        },
      },
      searchQuery: {
        default: null,
        parseHTML: element => element.getAttribute('data-search-query'),
        renderHTML: attributes => {
          if (!attributes.searchQuery) {
            return {};
          }
          return {
            'data-search-query': attributes.searchQuery,
          };
        },
      },
      imageCount: {
        default: 0,
        parseHTML: element => parseInt(element.getAttribute('data-image-count')) || 0,
        renderHTML: attributes => {
          return {
            'data-image-count': attributes.imageCount,
          };
        },
      },
      chapterTitle: {
        default: null,
        parseHTML: element => element.getAttribute('data-chapter-title'),
        renderHTML: attributes => {
          if (!attributes.chapterTitle) {
            return {};
          }
          return {
            'data-chapter-title': attributes.chapterTitle,
          };
        },
      },
      chapterNumber: {
        default: null,
        parseHTML: element => element.getAttribute('data-chapter-number'),
        renderHTML: attributes => {
          if (!attributes.chapterNumber) {
            return {};
          }
          return {
            'data-chapter-number': attributes.chapterNumber,
          };
        },
      },
    };
  },

  // Define commands for this extension (optional)
  addCommands() {
    return {
      setImageSuggestionCard: (attributes) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes,
        });
      },
    };
  },
});
