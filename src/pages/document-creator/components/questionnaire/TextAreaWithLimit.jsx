import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../../components/AppIcon';

/**
 * TextAreaWithLimit - Enhanced textarea component with character/word limits,
 * suggestions, and smart features for questionnaire forms
 */
const TextAreaWithLimit = ({
  value = '',
  onChange,
  placeholder = 'Enter your response...',
  maxLength = null,
  maxWords = null,
  minLength = null,
  minWords = null,
  rows = 4,
  autoResize = true,
  showCounter = true,
  required = false,
  disabled = false,
  className = '',
  label = '',
  description = '',
  helpText = '',
  suggestions = [],
  showSuggestions = true,
  onSuggestionClick = null,
  validateOnBlur = true,
  errorMessage = '',
  successMessage = '',
  autoFocus = false,
  spellCheck = true,
  resize = 'vertical', // 'none', 'vertical', 'horizontal', 'both'
}) => {
  const [focused, setFocused] = useState(false);
  const [showSuggestionsPanel, setShowSuggestionsPanel] = useState(false);
  const textareaRef = useRef(null);

  // Auto-resize functionality
  useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [value, autoResize]);

  // Auto-focus
  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  // Calculate character and word counts
  const charCount = value.length;
  const wordCount = value.trim() ? value.trim().split(/\s+/).length : 0;

  // Validation states
  const isCharLimitExceeded = maxLength && charCount > maxLength;
  const isWordLimitExceeded = maxWords && wordCount > maxWords;
  const isCharMinNotMet = minLength && charCount < minLength;
  const isWordMinNotMet = minWords && wordCount < minWords;
  const hasError = errorMessage || isCharLimitExceeded || isWordLimitExceeded;
  const hasSuccess = successMessage && !hasError;

  // Handle input change
  const handleChange = (e) => {
    const newValue = e.target.value;
    
    // Prevent input if character limit exceeded
    if (maxLength && newValue.length > maxLength) {
      return;
    }
    
    // Prevent input if word limit exceeded
    if (maxWords) {
      const newWordCount = newValue.trim() ? newValue.trim().split(/\s+/).length : 0;
      if (newWordCount > maxWords) {
        return;
      }
    }
    
    onChange(newValue);
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    if (onSuggestionClick) {
      onSuggestionClick(suggestion);
    } else {
      // Default behavior: append suggestion to current value
      const newValue = value ? `${value} ${suggestion}` : suggestion;
      onChange(newValue);
    }
    setShowSuggestionsPanel(false);
  };

  // Get border color based on state
  const getBorderColor = () => {
    if (hasError) return 'border-red-500 focus:border-red-500';
    if (hasSuccess) return 'border-green-500 focus:border-green-500';
    if (focused) return 'border-primary';
    return 'border-border hover:border-primary/50';
  };

  // Get counter color
  const getCounterColor = () => {
    if (isCharLimitExceeded || isWordLimitExceeded) return 'text-red-500';
    if (maxLength && charCount > maxLength * 0.8) return 'text-yellow-600';
    if (maxWords && wordCount > maxWords * 0.8) return 'text-yellow-600';
    return 'text-text-secondary';
  };

  // Get resize class
  const getResizeClass = () => {
    switch (resize) {
      case 'none': return 'resize-none';
      case 'horizontal': return 'resize-x';
      case 'both': return 'resize';
      default: return 'resize-y';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label and Description */}
      {(label || description) && (
        <div className="space-y-1">
          {label && (
            <label className="block text-sm font-medium text-text-primary">
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          {description && (
            <p className="text-sm text-text-secondary">{description}</p>
          )}
        </div>
      )}

      {/* Main Input Container */}
      <div className="relative">
        {/* Textarea */}
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleChange}
          onFocus={() => {
            setFocused(true);
            if (suggestions.length > 0 && showSuggestions) {
              setShowSuggestionsPanel(true);
            }
          }}
          onBlur={() => {
            setFocused(false);
            // Delay hiding suggestions to allow clicks
            setTimeout(() => setShowSuggestionsPanel(false), 150);
          }}
          placeholder={placeholder}
          rows={rows}
          disabled={disabled}
          spellCheck={spellCheck}
          className={`
            w-full px-3 py-2 border-2 rounded-lg transition-all duration-200
            focus:outline-none focus:ring-0 placeholder-gray-400
            disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50
            ${getBorderColor()}
            ${getResizeClass()}
          `}
        />

        {/* Suggestions Panel */}
        {showSuggestionsPanel && suggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto">
            <div className="p-2">
              <div className="text-xs text-text-secondary mb-2 font-medium">
                Suggestions:
              </div>
              <div className="space-y-1">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left px-2 py-1 text-sm rounded hover:bg-gray-100 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer Info */}
      <div className="flex items-center justify-between text-xs">
        {/* Help Text or Error/Success Messages */}
        <div className="flex-1">
          {hasError && (
            <div className="flex items-center space-x-1 text-red-500">
              <Icon name="AlertCircle" size={12} />
              <span>{errorMessage || 'Input exceeds limit'}</span>
            </div>
          )}
          {hasSuccess && (
            <div className="flex items-center space-x-1 text-green-500">
              <Icon name="CheckCircle" size={12} />
              <span>{successMessage}</span>
            </div>
          )}
          {!hasError && !hasSuccess && helpText && (
            <span className="text-text-secondary">{helpText}</span>
          )}
        </div>

        {/* Character/Word Counter */}
        {showCounter && (
          <div className={`flex items-center space-x-2 ${getCounterColor()}`}>
            {/* Character Count */}
            {maxLength && (
              <span>
                {charCount}/{maxLength} chars
              </span>
            )}
            
            {/* Word Count */}
            {maxWords && (
              <span>
                {wordCount}/{maxWords} words
              </span>
            )}
            
            {/* Minimum Requirements */}
            {(minLength || minWords) && (
              <div className="text-xs">
                {isCharMinNotMet && minLength && (
                  <span className="text-yellow-600">
                    Min {minLength} chars
                  </span>
                )}
                {isWordMinNotMet && minWords && (
                  <span className="text-yellow-600">
                    Min {minWords} words
                  </span>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Suggestions Toggle (when not focused) */}
      {!focused && suggestions.length > 0 && showSuggestions && (
        <button
          type="button"
          onClick={() => setShowSuggestionsPanel(!showSuggestionsPanel)}
          className="text-xs text-primary hover:text-primary/80 flex items-center space-x-1"
        >
          <Icon name="Lightbulb" size={12} />
          <span>Show suggestions</span>
        </button>
      )}
    </div>
  );
};

export default TextAreaWithLimit;
