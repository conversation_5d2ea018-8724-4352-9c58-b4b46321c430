import React, { useState, useEffect } from 'react';
import Button from '../../../../components/ui/Button';
import { generateTitles } from '../../../../services/aiService';

/**
 * TitleSelectionStep - Designrr-style title selection
 * Shows AI-generated title options with radio button selection
 */
const TitleSelectionStep = ({ 
  formData, 
  onInputChange, 
  onValidationChange,
  className = '' 
}) => {
  const [generatedTitles, setGeneratedTitles] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Generate titles using real AI service
  const generateTitlesForDocument = async () => {
    setIsGenerating(true);

    try {
      const topic = formData.topicAndNiche?.mainTopic || 'Life Coaching';
      const audience = formData.audienceAnalysis?.primaryAudience || 'parents';
      const subNiches = formData.topicAndNiche?.subNiches || [];
      const language = formData.topicAndNiche?.language || 'english';

      // Use real AI service with document type-specific prompts
      const aiTitles = await generateTitles({
        topic,
        audience,
        subNiches,
        language,
        documentData: formData
      });

      setGeneratedTitles(aiTitles);
      onInputChange('titleSelection.generatedTitles', aiTitles);

    } catch (error) {
      console.error('Error generating titles:', error);

      // Fallback to mock titles if AI fails
      const fallbackTitles = generateFallbackTitles(
        formData.topicAndNiche?.mainTopic || 'Life Coaching',
        formData.audienceAnalysis?.primaryAudience || 'parents'
      );
      setGeneratedTitles(fallbackTitles);
      onInputChange('titleSelection.generatedTitles', fallbackTitles);
    } finally {
      setIsGenerating(false);
    }
  };

  // Fallback title generation if AI fails
  const generateFallbackTitles = (topic, audience) => {
    const audienceCapitalized = audience.charAt(0).toUpperCase() + audience.slice(1);

    return [
      {
        id: 'title-1',
        text: `The Complete ${audienceCapitalized}'s Guide to ${topic}`,
        style: 'descriptive'
      },
      {
        id: 'title-2',
        text: `Mastering ${topic}: A ${audienceCapitalized}'s Journey`,
        style: 'professional'
      },
      {
        id: 'title-3',
        text: `${topic} Simplified: Essential Strategies for ${audienceCapitalized}`,
        style: 'catchy'
      },
      {
        id: 'title-4',
        text: `From Beginner to Expert: Your ${topic} Transformation`,
        style: 'inspirational'
      }
    ];
  };

  // Generate titles on component mount if not already generated
  useEffect(() => {
    if (formData.topicAndNiche?.mainTopic && formData.audienceAnalysis?.primaryAudience) {
      if (!formData.titleSelection?.generatedTitles?.length) {
        generateTitlesForDocument();
      } else {
        setGeneratedTitles(formData.titleSelection.generatedTitles);
      }
    }
  }, [formData.topicAndNiche?.mainTopic, formData.audienceAnalysis?.primaryAudience]);

  // Reset titles when AI content should be cleared (e.g., returning from editor)
  useEffect(() => {
    const topic = formData.topicAndNiche?.mainTopic;
    const audience = formData.audienceAnalysis?.primaryAudience;
    const generatedTitles = formData.titleSelection?.generatedTitles;

    // If we have topic and audience but no generated titles, it indicates a reset
    if (topic && audience && (!generatedTitles || generatedTitles.length === 0)) {
      setGeneratedTitles([]);
    }
  }, [formData.titleSelection?.generatedTitles]);

  const handleTitleSelect = (titleId) => {
    const selectedTitle = generatedTitles.find(title => title.id === titleId);
    onInputChange('titleSelection.selectedTitle', selectedTitle?.text || '');
  };

  const handleRegenerateTitles = () => {
    generateTitlesForDocument();
    // Clear current selection
    onInputChange('titleSelection.selectedTitle', '');
  };

  // Validation
  useEffect(() => {
    const isValid = formData.titleSelection?.selectedTitle?.trim().length > 0;
    onValidationChange?.(isValid);
  }, [formData.titleSelection?.selectedTitle]);

  return (
    <div className={`space-y-8 max-w-4xl mx-auto px-4 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl md:text-3xl font-bold text-text-primary mb-4">
          Select a title
        </h2>
        <p className="text-text-secondary text-base md:text-lg">
          Choose from AI-generated titles or create your own
        </p>
      </div>

      {/* Loading State */}
      {isGenerating && (
        <div className="text-center py-12">
          <div className="inline-flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="text-text-secondary">Generating titles...</span>
          </div>
        </div>
      )}

      {/* Title Options */}
      {!isGenerating && generatedTitles.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-text-primary">
              Generated Titles
            </h3>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleRegenerateTitles}
              iconName="RefreshCw"
              iconPosition="left"
              className="text-sm"
            >
              Regenerate
            </Button>
          </div>

          <div className="space-y-3">
            {generatedTitles.map((title) => (
              <label
                key={title.id}
                className={`
                  block p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md
                  ${formData.titleSelection?.selectedTitle === title.text
                    ? 'border-primary bg-primary/5 shadow-lg'
                    : 'border-border hover:border-primary/50'
                  }
                `}
              >
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    name="selectedTitle"
                    value={title.text}
                    checked={formData.titleSelection?.selectedTitle === title.text}
                    onChange={() => handleTitleSelect(title.id)}
                    className="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300"
                  />
                  <div className="flex-1">
                    <p className="text-base font-medium text-text-primary leading-relaxed">
                      {title.text}
                    </p>
                    <span className={`
                      inline-block mt-2 px-2 py-1 text-xs rounded-full
                      ${title.style === 'descriptive' ? 'bg-blue-100 text-blue-800' :
                        title.style === 'catchy' ? 'bg-green-100 text-green-800' :
                        'bg-purple-100 text-purple-800'
                      }
                    `}>
                      {title.style}
                    </span>
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Custom Title Option */}
      {!isGenerating && (
        <div className="space-y-4 pt-6 border-t border-border">
          <h3 className="text-lg font-semibold text-text-primary">
            Or create your own title
          </h3>
          <input
            type="text"
            placeholder="Enter your custom title..."
            value={formData.titleSelection?.customTitle || ''}
            onChange={(e) => {
              onInputChange('titleSelection.customTitle', e.target.value);
              onInputChange('titleSelection.selectedTitle', e.target.value);
            }}
            className="w-full h-12 md:h-14 text-base md:text-lg rounded-lg border-2 border-border focus:border-primary focus:ring-0 px-4"
          />
        </div>
      )}
    </div>
  );
};

export default TitleSelectionStep;
