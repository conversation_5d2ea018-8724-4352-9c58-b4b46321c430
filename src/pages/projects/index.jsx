
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import { useProjects } from '../../hooks/useProjects';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import { ProjectCard, ProjectsLoading, ProjectsError } from '../../components/projects';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import { getDocumentRoute } from '../../utils/progressUtils';
import { projectsService } from '../../services/projectsService';

const Projects = () => {
  const navigate = useNavigate();
  const { contentMargin } = useSidebar();
  const [activeCategory, setActiveCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  // Use the shared projects hook
  const { projects, isLoading, error, refetch } = useProjects({
    sortBy: sortBy === 'newest' ? 'updated_at' :
           sortBy === 'oldest' ? 'created_at' :
           sortBy === 'name' ? 'title' : 'progress',
    sortOrder: sortBy === 'oldest' ? 'asc' : 'desc'
  });

  // Categories for filtering - matching actual core document types
  const categories = [
    { id: 'all', label: 'All', count: projects.length },
    { id: 'ebooks', label: 'eBooks', count: projects.filter(p => p.category === 'eBooks').length },
    { id: 'academic', label: 'Academic', count: projects.filter(p => p.category === 'Academic').length },
    { id: 'business', label: 'Business', count: projects.filter(p => p.category === 'Business').length }
  ];

  // Sort options
  const sortOptions = [
    { value: 'newest', label: 'Newest' },
    { value: 'oldest', label: 'Oldest' },
    { value: 'name', label: 'Name' },
    { value: 'progress', label: 'Progress' }
  ];

  // Filter projects by category (sorting is now done by API)
  const filteredProjects = projects
    .filter(project => {
      // Show all projects when "all" is selected
      if (activeCategory === 'all') {
        return true;
      }

      const categoryMap = {
        'ebooks': 'eBooks',
        'academic': 'Academic',
        'business': 'Business'
      };
      return project.category === categoryMap[activeCategory];
    });

  const handleEditProject = (project) => {
    const route = getDocumentRoute(project);

    // Log for debugging
    console.log(`Navigating to project: ${project.title}`, {
      projectId: project.id,
      status: project.status,
      progress: project.progress,
      route: route
    });

    // For document creator route, pass documentId in state for editing existing documents
    if (route === '/document-creator') {
      navigate(route, { state: { documentId: project.id } });
    } else {
      navigate(route);
    }
  };

  const handleCreateProject = () => {
    navigate('/document-creator');
  };

  // Removed duplicate and share functionality as per requirements

  const handleDeleteProject = async (project) => {
    if (window.confirm(`Are you sure you want to delete "${project.title}"? This action cannot be undone.`)) {
      try {
        const response = await projectsService.deleteProject(project.id);
        if (response.success) {
          console.log('Project deleted successfully');
          refetch(); // Refresh the projects list
        } else {
          console.error('Failed to delete project:', response.error);
          alert('Failed to delete project. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting project:', error);
        alert('An error occurred while deleting the project.');
      }
    }
  };

  const handlePreviewProject = (project) => {
    console.log('Previewing project:', project.title);
    // Implement preview functionality
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <Header />
        <main className={`${contentMargin} ml-0 pt-16 sidebar-layout`}>
          <div className="px-6 py-8">
            <Breadcrumbs />
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-text-primary mb-6">Projects</h1>
            </div>
            <ProjectsLoading />
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <Header />
        <main className={`${contentMargin} ml-0 pt-16 sidebar-layout`}>
          <div className="px-6 py-8">
            <Breadcrumbs />
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-text-primary mb-6">Projects</h1>
            </div>
            <ProjectsError error={error} onRetry={refetch} />
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      <Header />

      <main className={`${contentMargin} ml-0 pt-16 sidebar-layout`}>
        <div className="px-6 py-8">
          <Breadcrumbs />

          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-text-primary mb-2">Projects</h1>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="primary"
                  onClick={handleCreateProject}
                  className="rounded-lg"
                >
                  <Icon name="Plus" size={16} />
                  Create
                </Button>
              </div>
            </div>

            {/* Category Tabs and Sort Controls */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              {/* Category Tabs */}
              <div className="flex items-center space-x-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border ${
                      activeCategory === category.id
                        ? 'bg-primary text-white border-primary shadow-sm'
                        : 'bg-surface text-text-secondary hover:text-text-primary border-border hover:border-primary hover:shadow-sm'
                    }`}
                  >
                    {category.label}
                    <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                      activeCategory === category.id
                        ? 'bg-white bg-opacity-20 text-white'
                        : 'bg-surface-secondary text-text-muted'
                    }`}>
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>

              {/* Sort Controls */}
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-text-secondary">Sort:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-surface border border-border rounded-lg px-3 py-2 text-sm text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent min-w-[120px]"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Projects Grid */}
          <div className="px-6 py-8">
            <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
              {filteredProjects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onClick={handleEditProject}
                  onPreview={handlePreviewProject}
                  onDelete={handleDeleteProject}
                />
              ))}
            </div>

            {/* Empty State */}
            {filteredProjects.length === 0 && (
              <div className="text-center py-12">
                <Icon name="FolderOpen" size={48} className="text-text-muted mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-text-primary mb-2">
                  No projects found
                </h3>
                <p className="text-text-secondary mb-6">
                  {activeCategory === 'all'
                    ? 'No projects found. Create your first project to get started.'
                    : `No ${activeCategory} projects found. Try a different category or create a new project.`
                  }
                </p>
                <Button
                  variant="primary"
                  onClick={handleCreateProject}
                  className="rounded-lg"
                >
                  <Icon name="Plus" size={16} />
                  Create Your First Project
                </Button>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default Projects;
