import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';


const PreferencesSection = () => {
  const [preferences, setPreferences] = useState({
    language: "english",
    defaultTone: "academic",
    defaultFormat: "pdf",
    autoSave: true,
    emailNotifications: true,
    pushNotifications: false,

    marketingEmails: false,
    weeklyDigest: true,
    documentSharing: "public",
    defaultPrivacy: "private",
    timezone: "africa_lagos",
    dateFormat: "dd/mm/yyyy",
    numberFormat: "comma_separated"
  });

  const [hasChanges, setHasChanges] = useState(false);

  const languageOptions = [
    { value: "english", label: "English", flag: "🇬🇧" },
    { value: "yoruba", label: "Yorùbá", flag: "🇳🇬" },
    { value: "french", label: "Français", flag: "🇫🇷" }
  ];

  const toneOptions = [
    { value: "academic", label: "Academic", description: "Formal and scholarly tone" },
    { value: "conversational", label: "Conversational", description: "Friendly and approachable tone" },
    { value: "professional", label: "Professional", description: "Business-appropriate tone" },
    { value: "creative", label: "Creative", description: "Engaging and expressive tone" }
  ];

  const formatOptions = [
    { value: "pdf", label: "PDF", icon: "FileText" },
    { value: "docx", label: "Word Document", icon: "File" },
    { value: "epub", label: "EPUB", icon: "Book" },
    { value: "mobi", label: "MOBI", icon: "Smartphone" }
  ];

  const timezoneOptions = [
    { value: "africa_lagos", label: "West Africa Time (WAT)" },
    { value: "utc", label: "Coordinated Universal Time (UTC)" },
    { value: "africa_cairo", label: "Central Africa Time (CAT)" }
  ];

  const handlePreferenceChange = (key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    console.log('Saving preferences:', preferences);
    setHasChanges(false);
  };

  const handleReset = () => {
    // Reset to default values
    setPreferences({
      language: "english",
      defaultTone: "academic",
      defaultFormat: "pdf",
      autoSave: true,
      emailNotifications: true,
      pushNotifications: false,

      marketingEmails: false,
      weeklyDigest: true,
      documentSharing: "public",
      defaultPrivacy: "private",
      timezone: "africa_lagos",
      dateFormat: "dd/mm/yyyy",
      numberFormat: "comma_separated"
    });
    setHasChanges(false);
  };

  return (
    <div className="space-y-6">
      {/* Save/Reset Actions */}
      {hasChanges && (
        <div className="bg-warning/10 border border-warning/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name="AlertTriangle" size={16} color="var(--color-warning)" />
              <span className="text-sm text-warning">You have unsaved changes</span>
            </div>
            <div className="flex space-x-2">
              <Button variant="ghost" onClick={handleReset}>
                Reset
              </Button>
              <Button variant="primary" onClick={handleSave} iconName="Save" iconPosition="left">
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Language & Localization */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Language & Localization</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">Interface Language</label>
            <div className="space-y-2">
              {languageOptions.map((option) => (
                <label key={option.value} className="flex items-center space-x-3 p-3 border border-border rounded-lg hover:bg-background cursor-pointer">
                  <input
                    type="radio"
                    name="language"
                    value={option.value}
                    checked={preferences.language === option.value}
                    onChange={(e) => handlePreferenceChange('language', e.target.value)}
                    className="text-primary focus:ring-primary"
                  />
                  <span className="text-lg">{option.flag}</span>
                  <span className="text-sm text-text-primary">{option.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">Regional Settings</label>
            <div className="space-y-4">
              <div>
                <label className="block text-xs text-text-secondary mb-2">Timezone</label>
                <select
                  value={preferences.timezone}
                  onChange={(e) => handlePreferenceChange('timezone', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  {timezoneOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-xs text-text-secondary mb-2">Date Format</label>
                <select
                  value={preferences.dateFormat}
                  onChange={(e) => handlePreferenceChange('dateFormat', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="dd/mm/yyyy">DD/MM/YYYY</option>
                  <option value="mm/dd/yyyy">MM/DD/YYYY</option>
                  <option value="yyyy-mm-dd">YYYY-MM-DD</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Document Defaults */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Document Defaults</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">Default Tone</label>
            <div className="space-y-2">
              {toneOptions.map((option) => (
                <label key={option.value} className="flex items-start space-x-3 p-3 border border-border rounded-lg hover:bg-background cursor-pointer">
                  <input
                    type="radio"
                    name="defaultTone"
                    value={option.value}
                    checked={preferences.defaultTone === option.value}
                    onChange={(e) => handlePreferenceChange('defaultTone', e.target.value)}
                    className="text-primary focus:ring-primary mt-1"
                  />
                  <div>
                    <span className="text-sm font-medium text-text-primary">{option.label}</span>
                    <p className="text-xs text-text-secondary">{option.description}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">Default Export Format</label>
            <div className="space-y-2">
              {formatOptions.map((option) => (
                <label key={option.value} className="flex items-center space-x-3 p-3 border border-border rounded-lg hover:bg-background cursor-pointer">
                  <input
                    type="radio"
                    name="defaultFormat"
                    value={option.value}
                    checked={preferences.defaultFormat === option.value}
                    onChange={(e) => handlePreferenceChange('defaultFormat', e.target.value)}
                    className="text-primary focus:ring-primary"
                  />
                  <Icon name={option.icon} size={16} color="var(--color-secondary)" />
                  <span className="text-sm text-text-primary">{option.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Notification Preferences</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-text-primary">Email Notifications</h4>
              <p className="text-xs text-text-secondary">Receive important updates via email</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.emailNotifications}
                onChange={(e) => handlePreferenceChange('emailNotifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-background peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-text-primary">Push Notifications</h4>
              <p className="text-xs text-text-secondary">Browser notifications for real-time updates</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.pushNotifications}
                onChange={(e) => handlePreferenceChange('pushNotifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-background peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-text-primary">System Notifications</h4>
              <p className="text-xs text-text-secondary">Updates about system maintenance and new features</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.systemNotifications || false}
                onChange={(e) => handlePreferenceChange('systemNotifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-background peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-text-primary">Weekly Digest</h4>
              <p className="text-xs text-text-secondary">Summary of your weekly activity and achievements</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.weeklyDigest}
                onChange={(e) => handlePreferenceChange('weeklyDigest', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-background peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Privacy & Collaboration */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Privacy & Collaboration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">Default Document Privacy</label>
            <select
              value={preferences.defaultPrivacy}
              onChange={(e) => handlePreferenceChange('defaultPrivacy', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="private">Private (Only me)</option>
              <option value="team">Team (Organization members)</option>
              <option value="public">Public (Anyone with link)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-3">Document Sharing</label>
            <select
              value={preferences.documentSharing}
              onChange={(e) => handlePreferenceChange('documentSharing', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="disabled">Disabled</option>
              <option value="private">Private only</option>
              <option value="organization">Organization wide</option>
              <option value="public">Public sharing allowed</option>
            </select>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-text-primary">Auto-save Documents</h4>
              <p className="text-xs text-text-secondary">Automatically save changes while editing</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.autoSave}
                onChange={(e) => handlePreferenceChange('autoSave', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-background peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreferencesSection;