import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Configure Supabase client with optimized settings
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Disable email confirmation for development
    confirmationUrl: window.location.origin,
    // Set flow type to implicit to skip email confirmation
    flowType: 'implicit',
    // Fix for stuck sessions - reduce storage conflicts
    storageKey: 'sb-supabase-auth-token-v2',
    // Fix for token refresh issues
    autoRefreshToken: true,
    // Disable debug logs
    debug: false
  },
  // Add global error handler
  global: {
    headers: {
      'x-application-name': 'docforge-ai'
    }
  },
  // Add performance monitoring
  realtime: {
    // Disable realtime subscriptions if not needed
    enabled: false
  },
  // Add connection health check
  healthCheck: {
    enabled: true,
    interval: 30000 // 30 seconds
  }
})

// Add connection health check
let isSupabaseHealthy = true;
let lastConnectionCheck = 0;

export const checkSupabaseConnection = async (force = false) => {
  const now = Date.now();
  // Only check every 30 seconds unless forced
  if (!force && now - lastConnectionCheck < 30000) {
    return isSupabaseHealthy;
  }

  try {
    const startTime = performance.now();

    // Simple ping query
    const { data, error } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1)
      .maybeSingle();

    const duration = performance.now() - startTime;
    lastConnectionCheck = now;

    if (error) {
      console.error('❌ Supabase connection error:', error);
      isSupabaseHealthy = false;
      return false;
    }

    isSupabaseHealthy = true;
    return true;
  } catch (err) {
    console.error('❌ Supabase connection check failed:', err);
    isSupabaseHealthy = false;
    lastConnectionCheck = now;
    return false;
  }
}

// Auth helper functions
export const authHelpers = {
  // Sign up with email and password
  signUp: async (email, password, userData = {}) => {
    const signUpOptions = {
      email,
      password,
      options: {
        data: userData,
        // Try to skip email confirmation
        emailRedirectTo: undefined
      }
    }

    const { data, error } = await supabase.auth.signUp(signUpOptions)

    // If user is created but not confirmed, try to sign them in immediately
    if (data.user && !data.session && !error) {
      try {
        const signInResult = await supabase.auth.signInWithPassword({
          email,
          password
        })

        if (signInResult.data.session) {
          return { data: signInResult.data, error: null }
        }
      } catch (signInError) {
        // Return original signup result
      }
    }

    return { data, error }
  },

  // Sign in with email and password
  signIn: async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },

  // Sign out
  signOut: async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Reset password
  resetPassword: async (email) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    })
    return { data, error }
  },

  // Update password
  updatePassword: async (password) => {
    const { data, error } = await supabase.auth.updateUser({
      password
    })
    return { data, error }
  },

  // Get current user
  getCurrentUser: () => {
    return supabase.auth.getUser()
  },

  // Get current session
  getCurrentSession: () => {
    return supabase.auth.getSession()
  },

  // Listen to auth changes
  onAuthStateChange: (callback) => {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// Database helper functions
export const dbHelpers = {
  // Get user profile
  getUserProfile: async (userId) => {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()
    return { data, error }
  },

  // Update user profile
  updateUserProfile: async (userId, updates) => {
    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    return { data, error }
  },

  // Create user profile
  createUserProfile: async (profile) => {
    const { data, error } = await supabase
      .from('user_profiles')
      .insert([profile])
      .select()
      .single()
    return { data, error }
  }
}

export default supabase
