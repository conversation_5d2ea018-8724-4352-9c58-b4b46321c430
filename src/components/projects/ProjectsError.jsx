import React from 'react';
import Icon from '../AppIcon';
import Button from '../ui/Button';

const ProjectsError = ({ error, onRetry, compact = false }) => {
  return (
    <div className={`flex items-center justify-center ${compact ? 'h-32' : 'h-64'}`}>
      <div className="text-center">
        <div className="text-red-500 mb-4">
          <Icon name="AlertCircle" size={compact ? 32 : 48} />
        </div>
        <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold text-text-primary mb-2`}>
          Failed to Load Projects
        </h3>
        <p className="text-text-secondary mb-4">{error}</p>
        <Button onClick={onRetry} variant="primary" size={compact ? "sm" : "md"}>
          Try Again
        </Button>
      </div>
    </div>
  );
};

export default ProjectsError;
