import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Icon from '../AppIcon';
import Button from './Button';
import Input from './Input';
import QuickActionSidebar from './QuickActionSidebar';

const Header = () => {
  const { user, profile, signOut, loading } = useAuth();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const navigationItems = [
    { label: 'Dashboard', path: '/dashboard', icon: 'LayoutDashboard' },
    { label: 'Create', path: '/document-creator', icon: 'FileText' },
    { label: 'Templates', path: '/template-library', icon: 'Library' },
    { label: 'Verify', path: '/plagiarism-checker', icon: 'Shield' },
  ];

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {

      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const handleNavigation = (path) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const handleProfileClick = () => {
    setIsProfileOpen(!isProfileOpen);
  };

  const handleAccountSettings = () => {
    navigate('/account-settings');
    setIsProfileOpen(false);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      setIsProfileOpen(false);
      navigate('/auth');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-1000 bg-surface border-b border-border shadow-sm">
      <div className="px-4">
        <div className="flex items-center justify-between h-16">
          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden p-2 text-text-muted hover:text-text-primary transition-colors"
          >
            <Icon name="Menu" size={20} />
          </button>

          {/* Search Bar - Centered like reference */}
          <div className="flex-1 max-w-2xl mx-auto lg:mx-auto ps-8">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Icon name="Search" size={18} className="text-text-muted" />
              </div>
              <input
                type="search"
                placeholder="Search projects and docs"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-border rounded-md bg-background text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Help Icon */}
            <Button variant="ghost" className="p-2 hidden md:block">
              <Icon name="HelpCircle" size={18} className="text-text-muted" />
            </Button>

            {/* Notifications */}
            <Button variant="ghost" className="p-2 relative hidden md:block">
              <Icon name="Bell" size={18} className="text-text-muted" />
              <span className="absolute -top-1 -right-1 w-2 h-2 bg-error rounded-full"></span>
            </Button>

            {/* Create Button */}
            <Button
              onClick={() => navigate('/document-creator')}
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors duration-200"
            >
              Create
            </Button>

            {/* Profile Dropdown */}
            <div className="relative">
              <Button
                variant="ghost"
                onClick={handleProfileClick}
                className="p-1.5 rounded-full"
              >
                {profile?.avatar_url ? (
                  <img
                    src={profile.avatar_url}
                    alt={profile.full_name || user?.email}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {(profile?.full_name || user?.email || 'U').charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </Button>

              {isProfileOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-surface rounded-md shadow-elevated border border-border z-1100">
                  <div className="py-1">
                    <div className="px-4 py-2 border-b border-border">
                      <p className="text-sm font-medium text-text-primary">
                        {profile?.full_name || user?.email?.split('@')[0] || 'User'}
                      </p>
                      <p className="text-xs text-text-secondary">{user?.email}</p>
                      {profile?.user_type && (
                        <p className="text-xs text-text-secondary capitalize">{profile.user_type.replace('_', ' ')}</p>
                      )}
                    </div>
                    <button
                      onClick={handleAccountSettings}
                      className="w-full text-left px-4 py-2 text-sm text-text-primary hover:bg-background flex items-center space-x-2"
                    >
                      <Icon name="Settings" size={16} />
                      <span>Account Settings</span>
                    </button>
                    <button
                      onClick={handleLogout}
                      disabled={loading}
                      className="w-full text-left px-4 py-2 text-sm text-error hover:bg-background flex items-center space-x-2 disabled:opacity-50"
                    >
                      <Icon name="LogOut" size={16} />
                      <span>{loading ? 'Signing out...' : 'Sign Out'}</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Navigation Overlay */}
        {isMobileMenuOpen && (
          <div className="lg:hidden fixed inset-0 top-16 bg-black/50 z-1050" onClick={() => setIsMobileMenuOpen(false)}>
            <div className="w-64 h-full bg-surface border-r border-border shadow-elevated" onClick={(e) => e.stopPropagation()}>
              <div className="flex flex-col h-full">
                {/* Logo Section */}
                <div className="p-6 border-b border-border">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-primary to-hero-accent rounded-md flex items-center justify-center">
                      <Icon name="FileText" size={18} color="white" />
                    </div>
                    <span className="text-lg font-semibold text-text-primary">DocForge AI</span>
                  </div>
                </div>

                {/* Navigation Items */}
                <nav className="flex-1 p-4">
                  <div className="space-y-2">
                    {[
                      { label: 'Home', icon: 'Home', path: '/dashboard' },
                      { label: 'Projects', icon: 'FolderOpen', path: '/projects' },
                      { label: 'Docs', icon: 'FileText', path: '/document-creator' },
                      { label: 'Media', icon: 'Image', path: '/template-library' }
                    ].map((item) => (
                      <button
                        key={item.path}
                        onClick={() => {
                          navigate(item.path);
                          setIsMobileMenuOpen(false);
                        }}
                        className={`w-full flex items-center space-x-3 px-4 py-3 rounded-md text-sm font-medium transition-all duration-200 ${
                          location.pathname === item.path
                            ? 'bg-primary text-white shadow-sm'
                            : 'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                        }`}
                      >
                        <Icon name={item.icon} size={18} />
                        <span>{item.label}</span>
                      </button>
                    ))}
                  </div>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;