import React, { useState, useRef } from 'react';
import Icon from '../AppIcon';
import Button from './Button';
import { validatePdfFile } from '../../services/pdfExtractionService';

/**
 * PDF File Upload Component
 * Provides drag-and-drop and click-to-upload functionality for PDF files
 * Follows the existing design patterns from DocxUpload and URLInput components
 */
const PdfUpload = ({ 
  onFileSelect, 
  onValidationChange,
  disabled = false,
  className = "",
  showPreview = true
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [validation, setValidation] = useState({ isValid: false });
  const fileInputRef = useRef(null);

  // Handle drag events
  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  // Handle file selection and validation
  const handleFileSelection = (file) => {
    const validationResult = validatePdfFile(file);
    setValidation(validationResult);
    
    if (validationResult.isValid) {
      setSelectedFile(file);
      onFileSelect?.(file);
    } else {
      setSelectedFile(null);
      onFileSelect?.(null);
    }
    
    onValidationChange?.(validationResult);
  };

  // Trigger file input click
  const triggerFileInput = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Clear selected file
  const clearFile = () => {
    setSelectedFile(null);
    setValidation({ isValid: false });
    onFileSelect?.(null);
    onValidationChange?.({ isValid: false });
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Format file size for display
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get dropzone CSS classes
  const getDropzoneClasses = () => {
    let classes = `
      relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
      transition-all duration-200 min-h-[200px] flex items-center justify-center
    `;
    
    if (disabled) {
      classes += ' border-gray-200 bg-gray-50 cursor-not-allowed';
    } else if (isDragOver) {
      classes += ' border-blue-400 bg-blue-50';
    } else if (selectedFile && validation.isValid) {
      classes += ' border-green-300 bg-green-50';
    } else if (validation.errors?.length > 0) {
      classes += ' border-red-300 bg-red-50';
    } else {
      classes += ' border-gray-300 bg-gray-50 hover:border-blue-300 hover:bg-blue-50';
    }
    
    return classes;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* Drop zone */}
      <div
        className={getDropzoneClasses()}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={triggerFileInput}
      >
        <div className="space-y-4">
          {/* Icon and main message */}
          <div className="space-y-3">
            {selectedFile && validation.isValid ? (
              <Icon name="CheckCircle" size={48} className="mx-auto text-green-500" />
            ) : validation.errors?.length > 0 ? (
              <Icon name="AlertCircle" size={48} className="mx-auto text-red-500" />
            ) : (
              <Icon name="Upload" size={48} className="mx-auto text-text-secondary" />
            )}
            
            <div className="space-y-2">
              {selectedFile && validation.isValid ? (
                <div>
                  <p className="text-lg font-medium text-green-700">
                    PDF file ready for processing
                  </p>
                  <p className="text-sm text-green-600">
                    Click to select a different file
                  </p>
                </div>
              ) : validation.errors?.length > 0 ? (
                <div>
                  <p className="text-lg font-medium text-red-700">
                    Invalid PDF file
                  </p>
                  <p className="text-sm text-red-600">
                    Click to select a different file
                  </p>
                </div>
              ) : (
                <div>
                  <p className="text-lg font-medium text-text-primary">
                    {isDragOver ? 'Drop your PDF file here' : 'Upload PDF file'}
                  </p>
                  <p className="text-sm text-text-secondary">
                    Drag and drop or click to browse
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* File requirements */}
          <div className="text-xs text-text-muted space-y-1">
            <p>• Supported format: PDF (.pdf)</p>
            <p>• Maximum file size: 10MB</p>
            <p>• Text-based PDFs work best</p>
          </div>

          {/* Action button */}
          {!selectedFile ? (
            <Button
              variant="primary"
              onClick={(e) => {
                e.stopPropagation();
                triggerFileInput();
              }}
              disabled={disabled}
              iconName="FileText"
              iconPosition="left"
              className="mt-4"
            >
              Choose PDF File
            </Button>
          ) : (
            <Button
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                triggerFileInput();
              }}
              disabled={disabled}
              iconName="RefreshCw"
              iconPosition="left"
              className="mt-4"
            >
              Choose Different File
            </Button>
          )}
        </div>
      </div>

      {/* File preview section */}
      {showPreview && selectedFile && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Icon name="FileText" size={20} className="text-red-500" />
              <div>
                <p className="font-medium text-text-primary truncate max-w-xs">
                  {selectedFile.name}
                </p>
                <p className="text-sm text-text-secondary">
                  {formatFileSize(selectedFile.size)}
                </p>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                clearFile();
              }}
              disabled={disabled}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Icon name="X" size={16} />
            </Button>
          </div>
        </div>
      )}

      {/* Validation errors */}
      {validation.errors && validation.errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <Icon name="AlertCircle" size={16} className="text-red-500 mt-0.5 flex-shrink-0" />
            <div className="space-y-1">
              <p className="text-sm font-medium text-red-800">Please fix the following issues:</p>
              <ul className="text-sm text-red-700 space-y-1">
                {validation.errors.map((error, index) => (
                  <li key={index} className="flex items-start space-x-1">
                    <span className="text-red-500">•</span>
                    <span>{error}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PdfUpload;
