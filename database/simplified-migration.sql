-- DocForge AI - Simplified Projects Migration Script
-- Run this script in Supabase SQL Editor to set up the core projects system
-- This script is idempotent and can be run multiple times safely

-- Step 1: Create simplified projects schema
-- Projects table - Contains both metadata and content
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
    
    -- Basic project information
    title TEXT NOT NULL,
    description TEXT,
    document_type TEXT NOT NULL CHECK (document_type IN ('ebook', 'academic', 'business', 'guide', 'report', 'whitepaper', 'manual')),
    category TEXT NOT NULL CHECK (category IN ('eBooks', 'Academic', 'Business')),
    
    -- Project status and progress
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'review', 'completed', 'archived')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    
    -- Content data (JSONB for flexibility - matches current localStorage structure)
    questionnaire_data JSONB NOT NULL, -- Original Document Creator form data
    generated_content JSONB,           -- AI-generated content structure
    
    -- Computed metadata (auto-calculated from content)
    word_count INTEGER DEFAULT 0,
    chapter_count INTEGER DEFAULT 0,
    
    -- Visual and formatting
    thumbnail_url TEXT,
    format TEXT DEFAULT 'pdf' CHECK (format IN ('pdf', 'epub', 'docx', 'html')),
    
    -- Project settings
    is_template BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_accessed_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Soft delete
    deleted_at TIMESTAMPTZ,
    
    -- Search and indexing
    search_vector tsvector GENERATED ALWAYS AS (
        setweight(to_tsvector('english', title), 'A') ||
        setweight(to_tsvector('english', COALESCE(description, '')), 'B')
    ) STORED
);

-- Project activities table - For audit trail and activity feed
CREATE TABLE IF NOT EXISTS public.project_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    
    -- Activity details
    activity_type TEXT NOT NULL CHECK (activity_type IN (
        'created', 'updated', 'deleted', 'duplicated', 
        'published', 'archived', 'exported'
    )),
    activity_description TEXT,
    activity_data JSONB, -- Additional activity metadata
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 2: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON public.projects(user_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_document_type ON public.projects(document_type);
CREATE INDEX IF NOT EXISTS idx_projects_category ON public.projects(category);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON public.projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_updated_at ON public.projects(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_search_vector ON public.projects USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_projects_deleted_at ON public.projects(deleted_at) WHERE deleted_at IS NULL;

-- JSONB indexes for content queries
CREATE INDEX IF NOT EXISTS idx_projects_questionnaire_data ON public.projects USING GIN(questionnaire_data);
CREATE INDEX IF NOT EXISTS idx_projects_generated_content ON public.projects USING GIN(generated_content);

-- Activity indexes
CREATE INDEX IF NOT EXISTS idx_activities_project_id ON public.project_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_activities_user_id ON public.project_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON public.project_activities(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activities_type ON public.project_activities(activity_type);

-- Step 3: Enable Row Level Security
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_activities ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user ID
CREATE OR REPLACE FUNCTION auth.user_id() RETURNS UUID AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'sub',
    (auth.jwt() ->> 'user_id')
  )::UUID
$$ LANGUAGE SQL STABLE;

-- PROJECTS TABLE POLICIES
-- Users can view their own projects
CREATE POLICY "Users can view their own projects" ON public.projects
  FOR SELECT USING (user_id = auth.user_id());

-- Users can create their own projects
CREATE POLICY "Users can create their own projects" ON public.projects
  FOR INSERT WITH CHECK (user_id = auth.user_id());

-- Users can update their own projects
CREATE POLICY "Users can update their own projects" ON public.projects
  FOR UPDATE USING (user_id = auth.user_id());

-- Users can delete their own projects
CREATE POLICY "Users can delete their own projects" ON public.projects
  FOR DELETE USING (user_id = auth.user_id());

-- PROJECT ACTIVITIES TABLE POLICIES
-- Users can view activities for their own projects
CREATE POLICY "Users can view activities for their own projects" ON public.project_activities
  FOR SELECT USING (
    user_id = auth.user_id() OR
    EXISTS (
      SELECT 1 FROM public.projects p 
      WHERE p.id = project_id AND p.user_id = auth.user_id()
    )
  );

-- System can create activity logs
CREATE POLICY "System can create activity logs" ON public.project_activities
  FOR INSERT WITH CHECK (true);

-- Step 4: Create triggers and functions
-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to log project activities
CREATE OR REPLACE FUNCTION public.log_project_activity()
RETURNS TRIGGER AS $$
DECLARE
    activity_type_val TEXT;
    activity_desc TEXT;
    project_id_val UUID;
BEGIN
    -- Determine activity type based on operation
    IF TG_OP = 'INSERT' THEN
        activity_type_val := 'created';
        activity_desc := 'Project created';
        project_id_val := NEW.id;
    ELSIF TG_OP = 'UPDATE' THEN
        activity_type_val := 'updated';
        activity_desc := 'Project updated';
        project_id_val := NEW.id;
    ELSIF TG_OP = 'DELETE' THEN
        activity_type_val := 'deleted';
        activity_desc := 'Project deleted';
        project_id_val := OLD.id;
    END IF;

    -- Insert activity log
    INSERT INTO public.project_activities (
        project_id,
        user_id,
        activity_type,
        activity_description,
        activity_data
    ) VALUES (
        project_id_val,
        auth.user_id(),
        activity_type_val,
        activity_desc,
        CASE 
            WHEN TG_OP = 'UPDATE' THEN 
                jsonb_build_object(
                    'old_status', OLD.status,
                    'new_status', NEW.status,
                    'old_progress', OLD.progress,
                    'new_progress', NEW.progress
                )
            ELSE NULL
        END
    );

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
CREATE TRIGGER update_projects_updated_at
    BEFORE UPDATE ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER log_project_activity_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION public.log_project_activity();

-- Step 5: Grant necessary permissions
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.projects TO authenticated;
GRANT SELECT, INSERT ON public.project_activities TO authenticated;

-- Step 6: Verification (uncomment to test)
/*
-- Verify tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('projects', 'project_activities');

-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('projects', 'project_activities');
*/
